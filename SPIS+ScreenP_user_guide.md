# SPIS+ScreenP 用户使用手册 v1.1

## 📖 手册概述

本手册将详细指导您如何使用SPIS+ScreenP整合工具v1.1版本，包含完整的操作流程和界面说明。该工具集成了两个独立的学术研究工具，为您提供文献求助自动化和AI文献筛选功能。

### 🎯 工具概览
- **🔴 SPIS文献求助工具** - 红色主题窗口，用于自动化文献求助
- **🔵 ScreenP AI文献筛选工具** - 蓝色主题窗口，用于AI文献筛选

### ✨ v1.1版本新功能亮点
- **🔄 智能批次切换** - 自动管理浏览器窗口，无需手动操作
- **📝 可编辑批次详情** - Excel式数据编辑，支持增删改查
- **🪟 非模态窗口设计** - 多窗口同时操作，提升工作效率
- **⚡ 响应性优化** - 按钮响应更加灵敏，操作更流畅

## 🚀 程序启动

### 启动步骤
1. **双击运行**：双击 `SPIS+ScreenP.py` 文件
2. **命令行运行**：在命令提示符中输入 `python "SPIS+ScreenP.py"`
3. **等待加载**：程序会显示启动信息并加载界面

### 启动结果
程序成功启动后，您将看到：
- 命令行窗口显示启动信息
- 同时弹出两个独立的图形界面窗口
- 🔴 **SPIS工具窗口** - 位于屏幕左侧，红色主题
- 🔵 **ScreenP工具窗口** - 位于屏幕右侧，蓝色主题

## 📚 SPIS文献求助工具详细操作指南

### 界面布局说明

SPIS工具窗口从上到下包含以下区域：

#### 1. 标题栏
- **位置**：窗口最顶部
- **内容**：显示"📚 SPIS文献求助自动化工具"
- **样式**：红色渐变背景，白色文字

#### 2. 文件处理面板
- **位置**：标题下方第一个面板
- **标题**：📁 文件处理
- **功能**：选择和处理CSV文件

**详细组件说明**：
- **"📂 选择CSV文件"按钮**
  - 功能：打开文件选择对话框
  - 支持格式：CSV文件 (*.csv)
  - 点击后弹出文件浏览器
  
- **文件状态标签**
  - 初始显示："未选择文件"（灰色斜体）
  - 选择后显示：文件名（绿色粗体）
  
- **"🔄 处理文件并生成批次"按钮**
  - 初始状态：禁用（灰色）
  - 选择文件后：启用（红色）
  - 功能：处理CSV文件并生成批次信息

#### 3. 批次管理面板 ✨ 功能增强
- **位置**：文件处理面板下方
- **标题**：📊 批次管理
- **功能**：选择和管理处理批次

**详细组件说明**：
- **批次选择下拉框** ✨ 智能切换
  - 初始状态：禁用，显示空白
  - 处理文件后：显示"第1批次"、"第2批次"等选项
  - 功能：选择要处理的具体批次
  - ✨ **新功能**：切换批次时自动关闭当前浏览器并提示重新登录

- **"📋 查看批次详情"按钮** ✨ 可编辑详情
  - 功能：打开可编辑的批次详情窗口
  - ✨ **新功能**：非模态窗口设计，不阻塞主窗口操作
  - ✨ **新功能**：Excel式数据编辑功能
  - ✨ **新功能**：防重复打开，同一批次只能打开一个窗口

#### 4. 浏览器控制面板
- **位置**：批次管理面板下方
- **标题**：🌐 浏览器控制
- **功能**：控制Chrome浏览器和处理流程

**详细组件说明**：
- **"🚀 打开浏览器"按钮**
  - 初始状态：禁用
  - 处理文件后：启用
  - 功能：启动Chrome浏览器并打开SPIS网站
  
- **"▶️ 开始处理批次"按钮**
  - 初始状态：禁用
  - 打开浏览器后：启用
  - 功能：开始自动化处理选中的批次
  
- **"⏹️ 停止处理"按钮**
  - 初始状态：禁用
  - 处理过程中：启用
  - 功能：停止当前的处理任务

#### 5. 进度监控面板
- **位置**：窗口底部
- **标题**：📈 处理进度
- **功能**：显示处理进度和详细日志

**详细组件说明**：
- **进度条**
  - 初始状态：隐藏
  - 处理过程中：显示当前进度百分比
  - 颜色：蓝色渐变
  
- **状态标签**
  - 居中显示当前处理状态
  - 例如："就绪"、"正在处理第1/50篇"等
  
- **处理日志文本框**
  - 只读文本框，显示详细的处理日志
  - 自动滚动到最新内容
  - 包含时间戳和详细操作信息

## ✨ 可编辑批次详情窗口界面说明 (v1.1新增)

### 窗口概述
可编辑批次详情窗口是v1.1版本的重要新功能，提供Excel式的数据编辑体验，支持对批次数据进行增删改查操作。

### 窗口特性
- **非模态设计**：打开后不阻塞主窗口，可以同时操作
- **多窗口管理**：可以同时打开多个不同批次的详情窗口
- **智能防重复**：同一批次只能打开一个详情窗口
- **实时保存**：支持实时保存更改到Excel文件

### 界面布局详解

#### 1. 窗口标题栏
- **标题格式**：第X批次详情 - 可编辑
- **窗口大小**：1000x700像素，可调整
- **主题色彩**：与SPIS工具保持一致的红色主题

#### 2. 顶部信息栏
- **位置**：窗口最顶部
- **内容组成**：
  - **批次信息标签**：显示"第X批次详情 - 共Y条记录"
  - **状态指示器**：实时显示当前操作状态
    - 🟢 绿色："就绪" - 初始状态
    - 🟠 橙色："数据已修改 - 请保存更改" - 有未保存的修改
    - 🔵 蓝色："已添加新行 - 请填写数据并保存" - 添加了新记录
    - 🔴 红色："已删除X行 - 请保存更改" - 删除了记录

#### 3. 可编辑数据表格 ✨ 核心功能
- **位置**：窗口中央主要区域
- **功能特性**：
  - **Excel式编辑**：双击任意单元格开始编辑
  - **行选择**：点击行号选择整行（支持多选）
  - **列排序**：点击列标题进行数据排序
  - **交替行色**：提高数据可读性
  - **实时验证**：编辑时进行数据格式验证

**表格列说明**：
- **order列**：记录序号，自动生成
- **DOI列**：文献DOI信息，可编辑
- **batchN列**：批次编号，自动设置
- **Title列**：文献标题，可编辑

#### 4. 底部操作工具栏
- **位置**：窗口底部
- **按钮布局**：从左到右排列

**详细按钮说明**：

- **"➕ 添加行"按钮**
  - **功能**：在表格末尾添加新的空白行
  - **自动设置**：序号和批次号自动填充
  - **操作提示**：添加后状态栏显示提示信息
  - **响应优化**：点击任意位置立即响应

- **"➖ 删除选中行"按钮**
  - **功能**：删除当前选中的行（支持多选）
  - **安全确认**：删除前弹出确认对话框
  - **批量操作**：支持同时删除多行
  - **状态更新**：删除后更新状态提示

- **"🔄 重置"按钮**
  - **功能**：恢复数据到打开窗口时的原始状态
  - **安全确认**：重置前弹出确认对话框
  - **数据保护**：丢弃所有未保存的修改
  - **状态重置**：恢复到"就绪"状态

- **"💾 保存更改"按钮**
  - **功能**：将当前所有修改保存到Excel文件
  - **数据同步**：更新原始Excel文件中对应批次的数据
  - **成功提示**：保存成功后显示确认对话框
  - **错误处理**：保存失败时显示详细错误信息

- **"❌ 关闭"按钮**
  - **功能**：关闭当前详情窗口
  - **资源清理**：自动清理窗口引用和内存
  - **状态同步**：从主窗口的窗口管理器中移除

### 完整操作流程

#### 第一步：准备CSV文件
1. **文件格式要求**
   ```csv
   DOI,Title
   10.1016/j.example.2023.001,Example Paper Title 1
   10.1016/j.example.2023.002,Example Paper Title 2
   ```

2. **数据要求**
   - 必须包含DOI和Title两列（列名不区分大小写）
   - DOI可以是标准DOI格式或文献标题
   - 支持UTF-8、GBK、GB2312等编码格式

#### 第二步：导入和处理文件
1. **选择文件**
   - 点击"📂 选择CSV文件"按钮
   - 在弹出的文件对话框中选择您的CSV文件
   - 确认选择后，文件路径会显示在按钮右侧

2. **处理文件**
   - 点击"🔄 处理文件并生成批次"按钮
   - 程序会自动执行以下操作：
     - 检测文件编码和分隔符
     - 提取DOI和Title列
     - 清洗数据（空DOI用Title填充）
     - 添加序号和批次信息
     - 生成Excel文件

3. **查看处理结果**
   - 处理完成后，批次选择框会显示可用批次
   - 文件状态会显示"文件处理完成！共生成 X 个批次"
   - ✨ **新功能**：可以点击"📋 查看批次详情"打开可编辑的详情窗口

#### ✨ 第二步半：使用可编辑批次详情功能 (v1.1新增)
1. **打开批次详情窗口**
   - 在批次选择框中选择要查看的批次
   - 点击"📋 查看批次详情"按钮
   - 程序会打开一个非模态的可编辑详情窗口
   - 主窗口功能仍可正常使用

2. **编辑批次数据**
   - **查看数据**：窗口显示该批次的所有记录
   - **编辑单元格**：双击任意单元格开始编辑
   - **添加记录**：点击"➕ 添加行"按钮添加新记录
   - **删除记录**：选择行后点击"➖ 删除选中行"按钮
   - **状态提示**：顶部状态栏实时显示操作状态

3. **保存和管理更改**
   - **保存更改**：点击"💾 保存更改"按钮将修改保存到Excel文件
   - **重置数据**：点击"🔄 重置"按钮恢复到原始状态
   - **关闭窗口**：点击"❌ 关闭"按钮关闭详情窗口
   - **多窗口操作**：可以同时打开多个不同批次的详情窗口

#### 第三步：启动浏览器和智能批次切换 ✨ 功能增强
1. **打开浏览器**
   - 确保已安装Chrome浏览器
   - 确保chromedriver.exe在程序目录下
   - 点击"🚀 打开浏览器"按钮

2. **浏览器操作**
   - 程序会自动启动Chrome浏览器
   - 自动导航到SPIS网站
   - 弹出提示对话框，要求手动登录

3. **手动登录**
   - 在浏览器中输入您的SPIS账户信息
   - 完成登录后，关闭提示对话框
   - 返回SPIS工具窗口

4. **✨ 智能批次切换功能** (v1.1新增)
   - **自动检测**：当您在批次选择框中选择不同批次时，程序自动检测到变化
   - **自动关闭浏览器**：程序会自动关闭当前的浏览器窗口
   - **友好提示**：显示提示对话框说明切换原因：
     ```
     已切换到第X批次。

     由于一个登录账号一次只能下载50篇文献，
     程序已自动关闭当前浏览器。

     请点击'🚀 打开浏览器'按钮重新登录。
     ```
   - **状态重置**：自动重置相关按钮的启用/禁用状态
   - **重新登录**：点击"🚀 打开浏览器"按钮重新打开浏览器并登录

#### 第四步：开始自动化处理
1. **选择批次**
   - 在批次选择下拉框中选择要处理的批次
   - 可以先查看批次详情确认内容

2. **开始处理**
   - 点击"▶️ 开始处理批次"按钮
   - 确认开始处理的对话框中点击"是"

3. **处理过程**
   对每条记录，程序会执行以下步骤：
   - **自动操作**：填写DOI到搜索框
   - **手动操作**：您需要点击"搜文章"按钮
   - **手动操作**：在结果页点击"文献求助"按钮
   - **自动操作**：程序自动勾选服务条款
   - **手动操作**：您需要点击"确定"按钮
   - **自动操作**：程序继续处理下一条记录

4. **监控进度**
   - 进度条显示整体处理进度
   - 状态标签显示当前处理的文献
   - 日志文本框显示详细的处理信息

#### 第五步：完成处理
1. **处理完成**
   - 所有记录处理完成后，会显示完成对话框
   - 包含成功处理数量、失败数量和总耗时

2. **查看结果**
   - 成功和失败的详细信息会记录在日志中
   - 可以根据需要处理下一个批次

## 🔬 ScreenP AI文献筛选工具详细操作指南

### 界面布局说明

ScreenP工具窗口采用滚动布局，从上到下包含以下区域：

#### 1. 标题栏
- **位置**：窗口最顶部
- **内容**：显示"🔬 AI文献筛选自动化工具"
- **样式**：蓝色渐变背景，白色文字

#### 2. AI模型配置面板
- **位置**：标题下方第一个面板
- **标题**：🤖 AI模型配置
- **功能**：配置AI平台和模型参数

**详细组件说明**：
- **AI平台选择下拉框**
  - 选项：DeepSeek、ChatAnywhere
  - 默认：ChatAnywhere
  - 切换平台会自动更新可用模型列表
  
- **API Key输入框**
  - 默认：预设的API密钥（密码模式显示）
  - 可以输入自定义API密钥
  - 右侧有显示/隐藏按钮（👁/🙈）
  
- **模型选择下拉框**
  - 显示当前平台的所有可用模型
  - 默认：gemini-2.5-flash-preview-04-17
  - 下拉框较宽，完整显示模型名称
  
- **Max Tokens配置**
  - 滑块：0-8192范围
  - 输入框：精确数值输入
  - 默认值：4096
  - 滑块和输入框联动
  
- **Temperature配置**
  - 滑块：0-2.00范围
  - 输入框：支持小数点后两位
  - 默认值：0.70
  - 滑块和输入框联动

#### 3. 筛选标准提示词面板
- **位置**：AI配置面板下方
- **标题**：📝 筛选标准提示词
- **功能**：编辑和管理筛选提示词

**详细组件说明**：
- **提示词文本编辑器**
  - 多行文本编辑框，高度300像素
  - 预设专业的筛选标准提示词
  - 支持自定义编辑和修改
  - 针对"AI决策与责任归因"主题优化

#### 4. 数据导入与任务配置面板
- **位置**：提示词面板下方
- **标题**：📊 数据导入与任务配置
- **功能**：导入文件和配置处理参数

**详细组件说明**：
- **文件导入区域**
  - **"📁 导入文献文件"按钮**：打开文件选择对话框
  - **文件状态标签**：显示导入文件的信息
  
- **处理范围设置区域**
  - **起始行输入框**：设置处理的起始行号（默认1）
  - **结束行输入框**：设置处理的结束行号
  - **"📋 全部行"按钮**：快速设置为处理全部数据
  - **范围信息标签**：显示将要处理的记录数量
  
- **批次配置区域**
  - **总批次数输入框**：设置分批处理的批次数（默认5）
  - **批次信息标签**：显示每批次的记录数量
  
- **实时保存配置**
  - **"启用实时保存"复选框**：默认勾选
  - 功能：每处理一条记录立即保存到文件
  
- **控制按钮区域**
  - **"🧪 测试运行 (前5条)"按钮**：处理前5条记录验证配置
  - **"🚀 正式运行"按钮**：开始批量处理
  - **"⏹ 中止"按钮**：停止当前处理任务

#### 5. 进度监控面板
- **位置**：窗口底部
- **标题**：📈 进度监控
- **功能**：显示处理进度和详细过程

**详细组件说明**：
- **主进度条**
  - 初始状态：隐藏
  - 处理过程中：显示总体进度
  - 颜色：蓝色渐变
  
- **状态标签**
  - 居中显示，包含详细的进度信息
  - 格式："状态 (当前/总数) - 已用时: X分Y秒 - 预计剩余: X分Y秒"
  
- **处理过程详情文本框**
  - 高度250像素，只读
  - 显示极其详细的处理过程信息
  - 包含AI API调用详情、响应内容等
  - 自动滚动到最新内容
  
- **"💾 导出结果"按钮**
  - 初始状态：禁用
  - 处理完成后：启用
  - 功能：导出筛选结果到Excel或CSV文件

### 完整操作流程

#### 第一步：配置AI模型
1. **选择AI平台**
   - 在"选择AI平台"下拉框中选择平台
   - 推荐：ChatAnywhere（模型选择更多）
   - 切换平台会自动更新模型列表

2. **配置API密钥**
   - 默认已预设API密钥，可直接使用
   - 如需使用自定义密钥，在API Key输入框中输入
   - 点击👁按钮可显示/隐藏密钥内容

3. **选择AI模型**
   - 在"选择模型"下拉框中选择具体模型
   - 推荐：gemini-2.5-flash-preview-04-17（性能优秀）
   - 不同模型的处理效果可能有差异

4. **调整参数**
   - **Max Tokens**：控制AI回复的最大长度
     - 默认4096通常足够
     - 可根据需要调整到更高值
   - **Temperature**：控制AI回复的创造性
     - 默认0.70平衡准确性和多样性
     - 降低值获得更一致的结果

#### 第二步：配置筛选标准
1. **查看默认提示词**
   - 程序预设了专业的筛选标准提示词
   - 针对"AI决策与责任归因"主题优化
   - 包含详细的评估标准和输出格式

2. **自定义提示词（可选）**
   - 可以根据您的研究需求修改提示词
   - 建议保持原有的结构和格式
   - 确保包含明确的评估标准

#### 第三步：导入文献数据
1. **准备数据文件**
   ```csv
   title,Abstract Note
   "AI Decision Making in Healthcare","This paper explores..."
   "Responsibility Attribution in Systems","This study examines..."
   ```

2. **文件格式要求**
   - 支持Excel (.xlsx, .xls) 和CSV文件
   - 必须包含title和Abstract Note列
   - 列名支持多种变体（不区分大小写）
   - 标题和摘要不能为空

3. **导入文件**
   - 点击"📁 导入文献文件"按钮
   - 选择您的文献数据文件
   - 程序会自动检测编码和列名
   - 显示导入报告，包含有效记录数量

#### 第四步：配置处理参数
1. **设置处理范围**
   - **起始行**：设置从第几行开始处理（默认1）
   - **结束行**：设置处理到第几行（默认全部）
   - **快速设置**：点击"📋 全部行"处理所有数据
   - **范围验证**：程序会显示将要处理的记录数量

2. **配置批次数量**
   - 设置总批次数（默认5批次）
   - 程序会自动计算每批次的记录数量
   - 批次信息会实时更新显示

3. **确认实时保存**
   - 建议保持"启用实时保存"勾选状态
   - 每处理一条记录立即保存，防止数据丢失
   - 输出文件会自动命名并包含时间戳

#### 第五步：执行筛选任务
1. **测试运行（推荐）**
   - 首次使用建议先点击"🧪 测试运行 (前5条)"
   - 验证AI配置是否正确
   - 检查筛选结果是否符合预期
   - 测试完成后查看结果文件

2. **正式运行**
   - 确认配置无误后，点击"🚀 正式运行"
   - 程序开始批量处理文献数据
   - 可以随时点击"⏹ 中止"停止处理

3. **监控处理过程**
   - **进度条**：显示整体处理进度
   - **状态信息**：显示当前处理状态和时间估算
   - **详细日志**：显示每条记录的处理详情
     - 记录编号和批次信息
     - AI API调用状态
     - AI原始回复内容
     - 处理结果和保存状态

#### 第六步：查看和导出结果
1. **实时结果查看**
   - 如果启用了实时保存，可以随时打开结果文件查看
   - 文件名格式：`筛选结果_YYYYMMDD_HHMMSS.xlsx`
   - 每条记录包含：序号、标题、摘要、AI评估结果

2. **最终结果导出**
   - 处理完成后，点击"💾 导出结果"按钮
   - 选择保存位置和文件格式（Excel或CSV）
   - 程序会显示导出成功的确认信息

3. **结果文件格式**
   ```
   序号 | title | Abstract Note | 符合度&判断理由
   1    | ...   | ...          | 符合度评估结果：85%
                                  判断理由：
                                  1. 主题A（AI决策）相关性分析：...
                                  2. 主题B（责任）相关性分析：...
                                  3. 综合评估与打分逻辑：...
   ```

## 🔧 高级功能和技巧

### SPIS工具高级技巧 ✨ v1.1增强

1. **智能批次管理策略** ✨ 新功能
   - 建议每批处理50条记录（默认设置）
   - ✨ **批次切换优化**：切换批次时让程序自动管理浏览器，无需手动操作
   - ✨ **数据编辑策略**：使用可编辑详情窗口预先检查和修正数据质量
   - ✨ **多窗口工作流**：可以同时打开多个批次详情窗口进行数据对比和编辑
   - 处理失败的记录会在日志中详细记录

2. **可编辑详情窗口最佳实践** ✨ 新功能
   - **数据预处理**：在开始自动化处理前，先用详情窗口检查数据质量
   - **实时修正**：发现DOI格式问题时，可以直接在详情窗口中修正
   - **批量编辑**：对于相似的修改，可以使用复制粘贴快速批量编辑
   - **及时保存**：重要修改后立即点击保存，避免数据丢失
   - **备份策略**：重要数据修改前可以先备份原始Excel文件

3. **浏览器操作优化**
   - 保持浏览器窗口在前台，便于手动操作
   - 如果网络较慢，可以适当等待页面加载完成
   - 遇到验证码等情况，可以暂停处理手动解决
   - ✨ **智能切换**：利用自动批次切换功能，避免手动管理多个浏览器窗口

4. **多窗口协作技巧** ✨ 新功能
   - **主窗口 + 详情窗口**：可以在查看详情的同时继续处理其他批次
   - **多批次对比**：同时打开多个批次详情窗口进行数据对比
   - **并行工作**：一个批次在处理时，可以编辑其他批次的数据

### ScreenP工具高级技巧
1. **分段处理策略**
   - 对于大文件，建议分段处理（如每次1000条）
   - 利用起始行和结束行功能，避免重复处理
   - 每段处理完成后检查结果质量

2. **AI模型选择建议**
   - **gemini-2.5-flash-preview-04-17**：速度快，质量高，推荐日常使用
   - **deepseek-chat**：逻辑性强，适合复杂分析
   - **o1-mini**：推理能力强，适合深度分析

3. **参数调优建议**
   - **Max Tokens**：4096适合大多数情况，复杂分析可调至8192
   - **Temperature**：0.7平衡性好，要求一致性可降至0.3

## ❓ 常见问题解答

### SPIS工具常见问题 ✨ v1.1更新

1. **Q: Chrome浏览器启动失败**
   - A: 检查chromedriver.exe是否在程序目录下
   - A: 确认Chrome浏览器版本与驱动版本匹配
   - A: 尝试更新Chrome浏览器到最新版本

2. **Q: CSV文件读取失败**
   - A: 检查文件是否包含DOI和Title列
   - A: 尝试用Excel打开文件另存为UTF-8格式
   - A: 确认文件没有被其他程序占用

3. **✨ Q: 批次详情窗口相关问题** (v1.1新增)
   - **Q: 批次详情窗口无法编辑**
     - A: 确保双击单元格激活编辑模式
     - A: 检查是否选中了正确的单元格

   - **Q: 保存更改失败**
     - A: 检查Excel文件是否被其他程序（如Excel）占用
     - A: 确认有足够的磁盘空间
     - A: 检查文件是否为只读状态

   - **Q: 多个批次窗口冲突**
     - A: 程序自动防止重复打开同一批次，点击会激活已有窗口
     - A: 如果出现异常，可以关闭所有详情窗口后重新打开

4. **✨ Q: 智能批次切换问题** (v1.1新增)
   - **Q: 切换批次后浏览器没有自动关闭**
     - A: 检查是否有多个Chrome进程在运行，手动关闭后重试
     - A: 等待几秒钟让程序完成关闭操作

   - **Q: 浏览器关闭后无法重新打开**
     - A: 等待5-10秒钟后再点击"打开浏览器"按钮
     - A: 检查Chrome进程是否完全关闭

   - **Q: 批次切换提示对话框重复出现**
     - A: 确保在处理完一个提示后再进行下一步操作
     - A: 避免快速连续切换批次

### ScreenP工具常见问题
1. **Q: AI API调用失败**
   - A: 检查网络连接是否稳定
   - A: 验证API密钥是否正确和有效
   - A: 确认API平台服务是否正常

2. **Q: 文件导入失败**
   - A: 检查文件是否包含title和Abstract Note列
   - A: 确认文件格式为支持的Excel或CSV格式
   - A: 检查文件是否损坏或被占用

## 📞 技术支持

如遇到问题，请按以下步骤排查：
1. 查看程序日志中的详细错误信息
2. 确认所有依赖库已正确安装
3. 检查网络连接和API配置
4. 参考本手册的故障排除部分

## 🎨 界面元素详细说明

### SPIS工具界面元素

#### 按钮状态说明
- **启用状态**：红色背景，白色文字，可点击
- **禁用状态**：灰色背景，深灰色文字，不可点击
- **悬停效果**：颜色变深，提供视觉反馈

#### 文本框和标签
- **只读文本框**：浅灰色背景，用于显示日志
- **状态标签**：
  - 灰色斜体：初始状态或等待状态
  - 绿色粗体：成功状态
  - 红色粗体：错误状态

#### 进度指示器
- **进度条**：蓝色渐变，显示百分比
- **状态文字**：实时更新当前操作状态

### ScreenP工具界面元素

#### 滑块控件
- **Max Tokens滑块**：
  - 拖动滑块或直接输入数值
  - 范围：0-8192
  - 实时同步显示

- **Temperature滑块**：
  - 精确到小数点后两位
  - 范围：0.00-2.00
  - 影响AI回复的随机性

#### 下拉选择框
- **平台选择**：切换会重置模型列表
- **模型选择**：宽度适配长模型名称
- **批次选择**：动态生成选项

#### 文本编辑器
- **提示词编辑器**：
  - 支持多行编辑
  - 语法高亮（如果支持）
  - 自动换行

## 📋 操作检查清单

### SPIS工具操作检查清单

#### 准备阶段
- [ ] 确认Chrome浏览器已安装
- [ ] 确认chromedriver.exe在程序目录
- [ ] 准备包含DOI和Title列的CSV文件
- [ ] 确认SPIS网站账户可正常登录

#### 文件处理阶段
- [ ] 成功选择CSV文件
- [ ] 文件状态显示为绿色
- [ ] 点击处理文件按钮
- [ ] 确认批次生成成功
- [ ] 查看批次详情验证数据

#### 浏览器控制阶段
- [ ] 成功启动Chrome浏览器
- [ ] 浏览器打开SPIS网站
- [ ] 手动登录账户成功
- [ ] 返回工具窗口

#### 处理执行阶段
- [ ] 选择要处理的批次
- [ ] 确认开始处理
- [ ] 监控进度条和日志
- [ ] 按提示进行手动操作
- [ ] 查看处理完成报告

### ScreenP工具操作检查清单

#### AI配置阶段
- [ ] 选择合适的AI平台
- [ ] 确认API密钥有效
- [ ] 选择合适的AI模型
- [ ] 调整Max Tokens和Temperature参数

#### 提示词配置阶段
- [ ] 查看默认提示词内容
- [ ] 根据需要修改提示词
- [ ] 确认筛选标准清晰

#### 数据导入阶段
- [ ] 准备符合格式的文献文件
- [ ] 成功导入文件
- [ ] 查看导入报告
- [ ] 确认数据质量

#### 参数配置阶段
- [ ] 设置合适的处理范围
- [ ] 配置批次数量
- [ ] 启用实时保存
- [ ] 验证配置信息

#### 执行阶段
- [ ] 先进行测试运行
- [ ] 检查测试结果
- [ ] 开始正式运行
- [ ] 监控处理过程
- [ ] 导出最终结果

## 🔍 详细错误处理指南

### SPIS工具错误处理

#### 文件相关错误
1. **"无法读取文件"错误**
   - **原因**：文件编码不支持或文件损坏
   - **解决方案**：
     - 用Excel打开文件，另存为UTF-8 CSV格式
     - 检查文件是否被其他程序占用
     - 确认文件路径中没有特殊字符

2. **"未找到DOI和Title列"错误**
   - **原因**：列名不匹配或列不存在
   - **解决方案**：
     - 确认文件包含DOI和Title列
     - 检查列名拼写是否正确
     - 支持的列名变体：doi/DOI、title/Title

#### 浏览器相关错误
1. **"启动浏览器失败"错误**
   - **原因**：chromedriver版本不匹配或路径错误
   - **解决方案**：
     - 下载匹配Chrome版本的chromedriver
     - 确认chromedriver.exe在程序目录
     - 更新Chrome浏览器到最新版本

2. **"页面元素未找到"错误**
   - **原因**：网页加载慢或页面结构变化
   - **解决方案**：
     - 等待页面完全加载后再操作
     - 检查网络连接是否稳定
     - 确认SPIS网站是否正常访问

### ScreenP工具错误处理

#### API相关错误
1. **"API调用失败"错误**
   - **原因**：网络问题、API密钥无效或服务不可用
   - **解决方案**：
     - 检查网络连接是否稳定
     - 验证API密钥是否正确
     - 尝试切换到其他AI平台
     - 检查API服务是否有使用限制

2. **"HTTP 429错误"**
   - **原因**：API调用频率超限
   - **解决方案**：
     - 等待一段时间后重试
     - 减少批次数量，降低调用频率
     - 检查API账户的使用配额

#### 数据相关错误
1. **"列名不匹配"错误**
   - **原因**：文件缺少必需的列
   - **解决方案**：
     - 确认文件包含title和Abstract Note列
     - 检查列名是否正确（支持多种变体）
     - 重新整理数据文件格式

2. **"数据为空"错误**
   - **原因**：标题或摘要字段为空
   - **解决方案**：
     - 清理数据，删除空记录
     - 补充缺失的标题或摘要信息
     - 检查数据导入过程是否正确

## 💡 最佳实践建议

### SPIS工具最佳实践

1. **文件准备最佳实践**
   - 使用Excel整理数据，确保格式统一
   - 保持DOI格式的一致性
   - 定期备份原始数据文件

2. **批次处理最佳实践**
   - 建议每批50条记录，避免单次处理过多
   - 分时段处理，避免长时间连续操作
   - 记录处理进度，便于断点续传

3. **浏览器操作最佳实践**
   - 保持浏览器窗口在前台
   - 避免在处理过程中切换标签页
   - 遇到异常时及时暂停处理

### ScreenP工具最佳实践

1. **AI配置最佳实践**
   - 首次使用建议选择gemini-2.5-flash-preview-04-17
   - 根据处理结果质量调整Temperature参数
   - 定期检查API密钥的有效性和配额

2. **数据处理最佳实践**
   - 大文件建议分段处理，每段1000-2000条
   - 启用实时保存，防止数据丢失
   - 定期检查处理结果的质量

3. **性能优化最佳实践**
   - 合理设置批次数量，平衡速度和稳定性
   - 避免同时运行其他占用网络的程序
   - 定期清理临时文件和日志

## 📊 结果分析指南

### SPIS工具结果分析

#### 成功率分析
- **高成功率（>90%）**：配置正确，网络稳定
- **中等成功率（70-90%）**：可能存在网络波动或部分DOI无效
- **低成功率（<70%）**：需要检查配置或数据质量

#### 失败原因分析
- **网络超时**：检查网络连接稳定性
- **页面元素未找到**：可能网站结构发生变化
- **DOI格式错误**：检查原始数据质量

### ScreenP工具结果分析

#### 符合度分布分析
- **高符合度（80-100%）**：高度相关的文献
- **中符合度（50-79%）**：部分相关的文献
- **低符合度（20-49%）**：相关性较低的文献
- **不符合（0-19%）**：基本不相关的文献

#### 质量评估指标
- **AI回复完整性**：检查是否包含完整的评估结果
- **评估逻辑性**：验证AI的判断理由是否合理
- **一致性检查**：相似文献的评估结果是否一致

## 🔄 维护和更新

### 定期维护任务

#### 每周维护
- [ ] 检查Chrome浏览器是否需要更新
- [ ] 验证API密钥是否仍然有效
- [ ] 清理临时文件和日志文件

#### 每月维护
- [ ] 更新chromedriver到最新版本
- [ ] 检查AI平台是否有新模型可用
- [ ] 备份重要的配置和数据文件

#### 按需维护
- [ ] 程序出现异常时检查依赖库版本
- [ ] 网站结构变化时更新相关配置
- [ ] 根据使用反馈优化操作流程

### 版本更新指南

1. **检查更新**
   - 定期查看是否有新版本发布
   - 阅读更新日志了解新功能和修复

2. **备份数据**
   - 更新前备份重要的配置文件
   - 保存当前的处理结果和日志

3. **测试新版本**
   - 在小规模数据上测试新版本
   - 确认所有功能正常后正式使用

---

**手册版本**: 1.1
**适用程序版本**: SPIS+ScreenP v1.1
**最后更新**: 2025年1月
**主要更新**:
- ✨ 新增可编辑批次详情窗口使用指南
- ✨ 新增智能批次切换功能说明
- ✨ 新增非模态窗口操作指南
- ✨ 更新高级技巧和最佳实践
- ✨ 新增v1.1版本常见问题解答
**文档长度**: 详细完整版 (v1.1增强)
