#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
快速启动脚本
"""

import sys
import os
import subprocess

def check_dependencies():
    """检查并安装依赖"""
    print("🔍 检查依赖包...")
    
    required_packages = [
        "PyQt6", "pandas", "requests", "openpyxl", "xlrd", "numpy"
    ]
    
    missing_packages = []
    for package in required_packages:
        try:
            __import__(package.lower().replace("-", "_"))
            print(f"✓ {package}")
        except ImportError:
            missing_packages.append(package)
            print(f"❌ {package} - 缺失")
    
    if missing_packages:
        print(f"\n📦 正在安装缺失的包: {', '.join(missing_packages)}")
        try:
            subprocess.run([
                sys.executable, "-m", "pip", "install", 
                *missing_packages
            ], check=True)
            print("✓ 依赖安装完成!")
        except subprocess.CalledProcessError as e:
            print(f"❌ 依赖安装失败: {e}")
            return False
    
    return True

def main():
    """主函数"""
    print("🔬 AI文献筛选工具 - 启动器")
    print("=" * 40)
    
    # 检查Python版本
    if sys.version_info < (3, 8):
        print("❌ 需要Python 3.8或更高版本")
        input("按回车键退出...")
        return
    
    print(f"✓ Python版本: {sys.version.split()[0]}")
    
    # 检查依赖
    if not check_dependencies():
        input("按回车键退出...")
        return
    
    # 启动主程序
    print("\n🚀 启动应用程序...")
    try:
        from main import main as app_main
        app_main()
    except Exception as e:
        print(f"❌ 启动失败: {e}")
        import traceback
        traceback.print_exc()
        input("按回车键退出...")

if __name__ == "__main__":
    main() 