调用AI来对文献一篇一篇地进行筛选。
第一，AI模型设置，请参见AI模型说明文件中的内容。

第二，设置提示词框，请参见名为“论文筛选提示词”文件中的内容。

第三，***导入excel（.xlsx或.xls)或.csv格式的文件（第一行为列名，余下每行为一篇论文的信息，每列为信息指标，如title等），一行一行地读取title列和Abstract Note列下的信息，将其作为一个整体，譬如，：格式为title:接第1行title列中的标题内容；abstract：接第1行Abstract Note列中的摘要内容。将其作为一个整体的段落。

第四，设置运行AI筛选按键和中止按键（随时中止整个过程）
设置进度条，让我可以将excel中的文件进行分批，并且可指定第批需要处理的文献的数量，并且基于需要处理的总行数（即总篇数），分批数，正处理的指数，本批次共多少条文献，正在处理第多少条文献来进行设置。
要有详细的处理过程，在处理进度框中。



第五，输出结果中与导入文件格式相同，一共有5列，第1列为文献序号，从数字1开始，第2列为导入文件中的title列和Abstract Note中的内容，第4列后

当点击运行AI筛选按键时，程序会将论文信息一行一行地提交给AI处理，AI会反馈类似下面的内容：
【符合度】：98%
【判断理由】：

核心论点匹配：论文核心明确探讨了AI在司法判决（一种AI辅助决策）中的应用所产生的“问责差距”（Accountability Gap），完全命中了筛选标准的核心。

关键词证据：标题和摘要中包含了“AI's Role in Judicial Decisions”（对应概念A）、“Accountability Gap”（对应概念B）、“responsible”（对应概念B）和“liability model”（对应概念B）等高度相关的关键词。

内容分析：摘要通过设问“is the judge, the software developer, or the state responsible?”直接点明了研究的“责任归因”焦点。此外，其目标是“propose a hybrid liability model”，表明研究内容是关于此问题的具体解决方案。

综合评价：该论文的研究焦点精准地落在AI辅助决策与责任追究的交叉领域，并且深入探讨了法律层面的解决方案，是与筛选标准高度契合的理想文献，因此给予极高的符合度。

我需要你将符合度评估中的百分比数字和判断理由后的所有文字内容（一条一条地）整个放到结果文件中对应被评估标题和摘要内容后2列中，列名分别为符合度和判断理由。
