# DOI自动化处理与文献下载程序

我需要一个自动化程序，能够从我提供的CSV文件（导出的条目.csv）中读取DOI号并执行以下操作：

## 第一阶段：文献求助处理
1. 从CSV文件中读取"DOI"列的内容，每行对应一篇论文的DOI号（如"10.1177/00472875231214743"）
2. 允许我选择使用哪个浏览器来执行自动化任务
3. 将这些DOI号一个接一个地输入到我已手动打开并登录的网站的搜索框中（如"搜索框及按键图"所示）
4. 每输入一个DOI号后，自动点击输入框右侧的蓝色"搜文章"按钮
5. 当搜索结果显示在当前页面后，找到文献条目下方的"文献求助"按钮（与"导出题录"、"收藏"等按钮并列，如"搜索结果图"所示）并点击
6. 当弹出"文献求助"窗口时（如"自动弹出窗口"图所示），自动勾选"请勾选《文献求助服务条款》"前面的复选框
7. 点击"确定"按钮完成当前DOI的处理
8. **每完成一个DOI的处理，显示实时进度信息（如"已处理 X/Y 篇，当前处理：[论文标题]"）**
9. 重复上述步骤，严格按照CSV文件中的顺序处理，如果DOI列中的条目少于50条，则处理所有条目；如果超过50条，则最多只处理前50条
10. **第一阶段完成后，显示进度报告，包括：成功处理的DOI数量、失败的DOI号列表（如有）及原因、总耗时等信息**

## 第二阶段：文献下载
1. 完成第一阶段后，在指定浏览器中打开指定的查找结果页面
2. 在该页面上，识别所有如"结果下载"图所示的下载结果项，每项包含论文标题、时间戳（如"2025-06-13 10:17:28"）和状态信息（如"待审核"）
3. **显示找到的符合时间条件的文献总数**
4. 对于所有时间戳在我指定日期及之前的文献，自动点击其"下载全文"按钮，文件将自动静默下载到浏览器默认下载文件夹中，无需额外干预
5. **每触发一次下载，显示实时进度信息（如"正在下载 X/Y 篇：[论文标题]"）**
6. **第二阶段完成后，显示任务总结信息，包括：成功触发下载的文献数量、符合时间条件的文献总数、总耗时等信息**

整个过程中，我只需要手动打开网站并登录，以及指定日期和浏览器，其余操作都由程序自动完成。程序应该能够处理导出的条目.csv文件中的DOI号，并按照上述步骤进行自动化处理，同时在每个步骤和阶段完成后提供详细的实时进度信息、进度报告和任务总结。