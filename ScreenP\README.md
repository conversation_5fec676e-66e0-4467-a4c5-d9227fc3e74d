# 🔬 AI文献筛选自动化工具

## 项目简介

这是一个基于AI的文献筛选自动化工具，可以帮助研究人员快速筛选和评估大量学术论文。工具支持多种AI平台，能够根据用户自定义的筛选标准自动分析论文的标题和摘要，给出符合度评估和详细判断理由。

### 主要特性
- 🤖 支持多个AI平台（DeepSeek、ChatAnywhere等70+模型）
- 📊 智能数据处理和清洗
- 🎯 **分段处理功能**：支持指定行数范围，防止中断重新处理
- 🚀 实时保存和进度监控
- 💾 自动文件管理和结果导出
- 🛡️ 强大的错误处理和重试机制

## 安装步骤

### 环境要求
- Python 3.8 或更高版本
- Windows 10/11, macOS 10.14+, 或 Ubuntu 18.04+
- 2GB+ RAM
- 稳定的网络连接

### 1. 克隆项目
```bash
git clone <repository-url>
cd ai-literature-screening-tool
```

### 2. 安装依赖
```bash
# 创建虚拟环境（推荐）
python -m venv venv

# 激活虚拟环境
# Windows:
venv\Scripts\activate
# macOS/Linux:
source venv/bin/activate

# 安装依赖
pip install -r requirements.txt
```

### 3. 创建requirements.txt文件
如果没有requirements.txt文件，请创建并添加以下内容：
```
PyQt6>=6.4.0
pandas>=1.3.0
requests>=2.25.0
openpyxl>=3.0.0
```

### 4. 运行程序
```bash
python main.py
```

### 可选：打包为独立可执行文件
```bash
# 安装打包工具
pip install pyinstaller

# 打包程序
pyinstaller --onefile --windowed main.py

# 可执行文件将在 dist/ 目录中生成
```

## 核心功能

### 🤖 AI模型配置
- **支持多个AI平台**：
  - DeepSeek（模型：deepseek-chat, deepseek-reasoner）
  - ChatAnywhere（支持GPT-4、Claude、Gemini等70+模型）
- **灵活的参数调节**：
  - Max Tokens：0-8192（控制回复长度）
  - Temperature：0.0-2.0（控制回复创造性）
- **API Key管理**：预设密钥，支持用户自定义替换

### 📝 筛选标准配置
- **提示词编辑器**：支持自定义筛选标准和评估要求
- **内置默认模板**：针对"AI决策与责任归因"主题的专业筛选标准
- **灵活定制**：可根据不同研究领域调整评估维度

### 📊 数据处理功能
- **文件格式支持**：Excel (.xlsx, .xls) 和 CSV 文件
- **智能列名识别**：自动识别标题和摘要列
- **数据清洗**：自动过滤空值和无效记录
- **编码兼容**：支持多种文件编码格式

### 🎯 **分段处理功能**（核心功能）
- **指定行数范围**：可设置起始行和结束行，支持分段处理
- **防中断设计**：避免因网络或技术问题导致全部重新处理
- **智能批次计算**：根据指定范围自动计算批次数和每批处理量
- **文件命名规范**：输出文件自动包含行数范围标识

### 🚀 运行模式
- **测试运行**：处理前5条记录，验证配置正确性
- **正式运行**：批量处理所有数据，支持实时保存
- **实时监控**：详细的处理过程显示和进度跟踪

### 💾 结果管理
- **实时保存**：每完成一条记录立即保存结果
- **多种导出格式**：支持Excel和CSV格式导出
- **自动文件打开**：处理完成后自动打开结果文件

## 快速开始

### 1. 基础配置

#### AI模型设置
1. 在"AI模型配置"面板选择所需的AI平台
2. 选择具体的模型（推荐：gemini-2.5-flash-preview-04-17）
3. 如需使用自定义API Key，请输入替换预设密钥
4. 调整Max Tokens和Temperature参数（默认值通常已优化）

#### 筛选标准设置
1. 在"筛选标准提示词"文本框输入您的评估要求
2. 或使用内置的默认提示词模板
3. 确保提示词清晰描述筛选标准和期望的输出格式

### 2. 数据导入

#### 文件准备
准备包含以下列的文件：
- **标题列**：`title`, `标题`, `titles`, `paper title`, `article title`
- **摘要列**：`abstract note`, `abstract`, `摘要`, `abstracts`, `note`, `abstract notes`, `summary`

#### 导入步骤
```python
# 示例：准备CSV文件
import pandas as pd

data = {
    'title': ['AI Decision Making in Healthcare', 'Machine Learning Ethics'],
    'abstract note': ['This paper discusses...', 'This study examines...']
}
df = pd.DataFrame(data)
df.to_csv('literature_data.csv', index=False, encoding='utf-8-sig')
```

### 3. **分段处理设置**（推荐使用）

#### 为什么需要分段处理？
- **网络稳定性**：长时间运行可能遇到网络中断
- **API限制**：避免触发平台的频率限制
- **时间管理**：可分时段处理大量数据
- **错误隔离**：单段出错不影响其他段的处理

#### 设置步骤
1. **设置处理范围**：
   - 在"处理范围设置"面板设置起始行和结束行
   - 例如：第1-500行、第501-1000行等
   - 点击"📋 全部行"可快速设置为处理全部数据

2. **批次配置**：
   - 系统会根据选定范围自动计算批次信息
   - 推荐每批处理100-200条记录

### 4. 运行处理

#### 测试运行（推荐）
```python
# 程序会自动执行以下流程：
# 1. 验证AI配置
# 2. 处理前5条记录
# 3. 检查输出格式
# 4. 生成测试报告
```

#### 正式运行
确认测试结果正常后，点击"🚀 正式运行"开始批量处理。

## API参考

### 主要类

#### AILiteratureScreeningTool
主窗口类，负责用户界面管理。

```python
class AILiteratureScreeningTool(QMainWindow):
    """AI文献筛选工具主窗口"""
    
    def __init__(self):
        """初始化主窗口"""
        super().__init__()
        self.data = None
        self.worker = None
        self.thread = None
        self.results = None
        self.init_ui()
    
    def import_file(self):
        """导入文献文件"""
        # 支持Excel和CSV格式
        # 自动识别列名和编码
        pass
    
    def test_screening(self):
        """测试运行 - 处理前5条记录"""
        pass
    
    def run_screening(self):
        """正式运行AI筛选"""
        pass
```

#### ProgressWorker
工作线程类，负责AI调用和数据处理。

```python
class ProgressWorker(QObject):
    """进度工作线程"""
    
    # 信号定义
    progress_updated = pyqtSignal(int, int, int, int, str)
    result_ready = pyqtSignal(dict)
    error_occurred = pyqtSignal(str)
    finished = pyqtSignal()
    
    def setup(self, data, platform_config, prompt, batch_count, max_tokens, temperature):
        """设置工作参数"""
        pass
    
    def call_ai_api(self, row_data):
        """调用AI API进行评估"""
        # 构建请求
        # 处理响应
        # 错误重试
        pass
```

### 配置参数

#### AI平台配置
```python
AI_PLATFORMS = {
    "deepseek": {
        "name": "DeepSeek",
        "api_key": "your-api-key",
        "base_url": "https://api.deepseek.com/v1/chat/completions",
        "models": ["deepseek-chat", "deepseek-reasoner"],
        "default_model": "deepseek-chat"
    },
    "chatanywhere": {
        "name": "ChatAnywhere",
        "api_key": "your-api-key", 
        "base_url": "https://api.chatanywhere.tech/v1/chat/completions",
        "models": ["gemini-2.5-flash-preview-04-17", ...],
        "default_model": "gemini-2.5-flash-preview-04-17"
    }
}
```

#### 默认提示词
```python
DEFAULT_PROMPT = """
### 【正式版】AI论文筛选任务提示词

#### 1. 角色与任务 (Role & Task)
你将扮演一名在学术界享有盛誉的资深学者...

#### 2. 核心筛选标准 (Core Screening Criteria)
你需要判断每篇论文是否直接且深入地探讨了以下两大主题的交叉领域：
- 主题A：AI决策或AI辅助决策
- 主题B：责任或责任归因

...
"""
```

## 示例代码

### 基本使用示例
```python
#!/usr/bin/env python3
import sys
from PyQt6.QtWidgets import QApplication
from main import AILiteratureScreeningTool

def main():
    # 创建应用
    app = QApplication(sys.argv)
    
    # 创建主窗口
    window = AILiteratureScreeningTool()
    window.show()
    
    # 运行应用
    sys.exit(app.exec())

if __name__ == "__main__":
    main()
```

### 分段处理示例
```python
# 场景：处理1200条文献记录，分3段进行

# 第一段：第1-400行
# 设置: 起始行=1, 结束行=400, 批次数=4
# 输出: literature_data-R1-400.xlsx

# 第二段：第401-800行  
# 设置: 起始行=401, 结束行=800, 批次数=4
# 输出: literature_data-R401-800.xlsx

# 第三段：第801-1200行
# 设置: 起始行=801, 结束行=1200, 批次数=4
# 输出: literature_data-R801-1200.xlsx
```

### 自定义AI调用示例
```python
def custom_ai_call(title, abstract, api_config):
    """自定义AI调用示例"""
    import requests
    
    headers = {
        "Content-Type": "application/json",
        "Authorization": f"Bearer {api_config['api_key']}"
    }
    
    data = {
        "model": api_config['model'],
        "messages": [
            {"role": "user", "content": f"title: {title}; abstract: {abstract}"}
        ],
        "max_tokens": 4096,
        "temperature": 0.7
    }
    
    response = requests.post(
        api_config['base_url'],
        headers=headers,
        json=data,
        timeout=60
    )
    
    if response.status_code == 200:
        result = response.json()
        return result['choices'][0]['message']['content']
    else:
        raise Exception(f"API调用失败: {response.status_code}")
```

### 结果文件处理示例
```python
import pandas as pd

def merge_results(file_list):
    """合并多个分段处理结果"""
    all_data = []
    
    for file_path in file_list:
        df = pd.read_excel(file_path)
        all_data.append(df)
    
    # 合并数据
    merged_df = pd.concat(all_data, ignore_index=True)
    
    # 按序号排序
    merged_df = merged_df.sort_values('序号')
    
    # 保存合并结果
    merged_df.to_excel('merged_results.xlsx', index=False)
    
    return merged_df

# 使用示例
files = [
    'literature_data-R1-400.xlsx',
    'literature_data-R401-800.xlsx', 
    'literature_data-R801-1200.xlsx'
]
merged_results = merge_results(files)
```

## 分段处理最佳实践

### 推荐分段策略
- **小数据集（<200条）**：使用全部行，分2-3批次
- **中等数据集（200-1000条）**：每次处理300-500条
- **大数据集（>1000条）**：每次处理500-800条，分多次完成

### 实践示例：处理1200条记录

#### 方案一：按数量分段
```python
# 分3段处理，每段400条
segments = [
    (1, 400),      # 第一段
    (401, 800),    # 第二段  
    (801, 1200)    # 第三段
]

for i, (start, end) in enumerate(segments, 1):
    print(f"处理第{i}段：第{start}-{end}行")
    # 在界面中设置起始行和结束行
    # 点击正式运行
    # 等待完成后进行下一段
```

#### 方案二：按时间分段
```python
# 每天处理一段，适合长期项目
daily_segments = [
    (1, 300),      # 第1天
    (301, 600),    # 第2天
    (601, 900),    # 第3天
    (901, 1200)    # 第4天
]
```

### 错误处理和恢复
```python
def check_processing_status(result_file):
    """检查处理状态"""
    if os.path.exists(result_file):
        df = pd.read_excel(result_file)
        processed_count = len(df)
        print(f"已处理记录数：{processed_count}")
        return processed_count
    return 0

def resume_processing(original_start, original_end, result_file):
    """恢复中断的处理"""
    processed_count = check_processing_status(result_file)
    
    if processed_count > 0:
        # 从中断点继续
        new_start = original_start + processed_count
        print(f"从第{new_start}行继续处理...")
        return new_start, original_end
    else:
        # 重新开始
        return original_start, original_end
```

## 高级功能

### API调用优化
- **重试机制**：网络异常时自动重试3次
- **延时控制**：每次调用间隔0.5秒，避免频率限制
- **错误分类**：区分网络错误、API错误和解析错误

### 性能监控
- **实时统计**：成功/失败记录数
- **处理速度**：每条记录约1.5秒（含延时）
- **资源使用**：显示Token消耗估算

### 错误处理
- **自动重试**：临时网络问题自动重试
- **错误记录**：失败记录标注具体原因
- **部分成功**：不影响已成功处理的记录

## 常见问题解答

### Q1：如何选择合适的分段大小？
**A**：建议根据以下因素决定：
- 网络稳定性：不稳定时选择较小分段（200-300条）
- 处理时间：每段控制在1-2小时内完成
- 数据重要性：重要数据使用较小分段，确保安全

### Q2：如果处理中断了怎么办？
**A**：
1. 检查已生成的结果文件，确认已处理的记录
2. 从中断点重新设置起始行继续处理
3. 启用实时保存功能，降低数据丢失风险

### Q3：不同分段的结果如何合并？
**A**：
1. 所有结果文件的"序号"列保持原始行号
2. 可以直接在Excel中复制粘贴合并
3. 按序号列升序排序即可恢复原始顺序

### Q4：如何提高处理成功率？
**A**：
- 选择稳定的网络环境
- 使用可靠的AI平台（推荐Gemini或GPT-4）
- 设置适当的批次大小（不要太大）
- 启用实时保存功能

### Q5：支持哪些文件格式？
**A**：
- **输入格式**：Excel (.xlsx, .xls), CSV (.csv)
- **输出格式**：与输入格式相同
- **编码支持**：UTF-8, GBK, GB2312等多种编码

### Q6：API密钥如何管理？
**A**：
- 程序内置预设密钥，可直接使用
- 支持用户输入自定义密钥替换
- 密钥在界面中加密显示，保护隐私

## 技术规格

### 系统要求
- **操作系统**：Windows 10/11, macOS 10.14+, Linux Ubuntu 18.04+
- **Python版本**：3.8, 3.9, 3.10, 3.11
- **内存要求**：2GB+ RAM
- **网络要求**：稳定的互联网连接

### 依赖库
```python
PyQt6>=6.4.0          # GUI框架
pandas>=1.3.0         # 数据处理
requests>=2.25.0      # HTTP请求
openpyxl>=3.0.0       # Excel文件支持
```

### 支持的文件编码
- UTF-8, UTF-8-BOM
- GBK, GB2312
- Latin1, CP1252

### 性能指标
- **处理速度**：每条记录1.5秒（含延时）
- **并发性**：单线程处理，避免API限制
- **内存使用**：典型使用下 < 1GB
- **错误率**：网络正常情况下 < 5%

## 版本更新日志

### v1.1.0（当前版本）
- ✨ 新增分段处理功能
- ✨ 支持指定行数范围处理
- ✨ 智能批次计算
- ✨ 防中断设计
- 🔧 优化文件命名规则
- 🔧 改进进度显示逻辑
- 🔧 ChatAnywhere默认模型更新为gemini-2.5-flash-preview-04-17

### v1.0.0
- 🎉 初始版本发布
- 支持多AI平台
- 基础文献筛选功能
- 实时保存和导出

## 贡献指南

### 开发环境设置
```bash
# 克隆仓库
git clone <repository-url>
cd ai-literature-screening-tool

# 创建开发环境
python -m venv dev-env
source dev-env/bin/activate  # Linux/macOS
# 或
dev-env\Scripts\activate     # Windows

# 安装开发依赖
pip install -r requirements-dev.txt
```

### 代码规范
- 遵循PEP 8代码风格
- 使用类型提示
- 添加详细的文档字符串
- 编写单元测试

### 提交流程
1. Fork项目
2. 创建功能分支
3. 提交代码
4. 创建Pull Request

## 许可证

本项目采用MIT许可证 - 查看 [LICENSE](LICENSE) 文件了解详情。

## 联系方式

如遇到问题或需要技术支持：
- 在使用过程中，程序会显示详细的错误信息
- 建议先进行测试运行验证配置
- 重要数据建议分段处理，确保安全
- 提交Issue到GitHub仓库

---

**💡 使用提示**：建议首次使用时先用小批量数据进行测试，熟悉流程后再处理大量数据。启用分段处理功能可以大大降低意外中断的风险，是处理大规模数据的最佳实践。 