# SPIS文献DOI自动化处理系统

## 项目概述

SPIS（Scholar Paper Information System）是一个基于Python的文献DOI自动化处理工具，专门设计用于批量处理学术文献的DOI号，并在SPIS学术网站上进行自动化文献求助操作。该系统结合了自动化和人工操作，能够显著提高文献请求的效率。

## 核心功能

### 1. 数据预处理
- 📊 **CSV转换**: 自动将CSV格式的文献数据转换为Excel格式
- 🔧 **数据清洗**: 智能检测分隔符（逗号、分号、制表符），只保留Title和DOI两列
- 🔄 **数据补完**: 自动将Title内容填充到空白的DOI字段
- 📝 **排序编号**: 为每条记录添加顺序编号（order列）

### 2. 批次管理
- 📋 **智能分批**: 按每批50条记录自动分组
- 🏷️ **批次标记**: 为每条记录自动添加批次号（batchN列）
- 📊 **统计报告**: 显示详细的分批统计信息
- 🎯 **精准处理**: 支持指定特定批次进行处理

### 3. 自动化操作
- 🌐 **浏览器控制**: 使用Selenium自动控制Chrome浏览器
- 🔍 **智能搜索**: 自动填写DOI到搜索框
- ☑️ **条款勾选**: 自动勾选文献求助服务条款
- 📈 **进度跟踪**: 实时显示处理进度和状态

### 4. 异常处理
- ⚠️ **错误捕获**: 记录处理失败的DOI及具体原因
- 📝 **详细日志**: 提供完整的调试和处理信息
- ⏱️ **超时控制**: 智能等待页面加载，最长等待时间可配置

## 系统要求

### 软件环境
- **Python**: 3.7及以上版本
- **操作系统**: Windows 10/11（已测试）
- **浏览器**: Google Chrome（最新版本）

### 依赖库
```bash
pip install pandas>=1.3.0
pip install openpyxl>=3.0.9
pip install selenium>=4.0.0
```

## 安装步骤

### 1. 环境准备
```bash
# 克隆或下载项目到本地
cd SPIS

# 安装Python依赖
pip install -r requirements.txt
```

### 2. ChromeDriver配置
1. 检查Chrome浏览器版本：`chrome://version/`
2. 从[ChromeDriver官网](https://chromedriver.chromium.org/)下载对应版本
3. 将`chromedriver.exe`放置在项目根目录

### 3. 数据文件准备
确保项目目录中包含`导出的条目.csv`文件，格式要求：
- 必须包含`Title`和`DOI`两列
- 编码格式：UTF-8
- 支持的分隔符：逗号、分号、制表符

## 使用方法

### 快速开始

1. **启动程序**
```bash
python spis.py
```

2. **查看分批统计**
程序会自动显示分批信息：
```
共计有DOI信息的文献：88篇
共分为2批
第1批：50篇
第2批：38篇
```

3. **选择批次处理**
```
请指定要处理的批次编号（如1、2、3...）：
请输入批次编号：1
```

4. **浏览器操作**
- 程序会自动打开Chrome浏览器
- 手动登录SPIS网站
- 按回车键继续自动化处理

### 详细流程

#### 第一步：数据预处理
```python
# 自动执行以下操作：
1. 读取 '导出的条目.csv'
2. 只保留 Title 和 DOI 两列
3. 将空DOI用Title内容填充
4. 转换为Excel格式保存
```

#### 第二步：批次分配
```python
# 自动添加两列：
- order: 顺序编号 (1, 2, 3, ...)
- batchN: 批次编号 (1, 1, 1, ..., 2, 2, 2, ...)
```

#### 第三步：自动化处理
对每个DOI执行：
1. 🔍 自动填写DOI到搜索框
2. 👆 **手动点击"搜文章"按钮**
3. 👆 **手动点击"文献求助"按钮**
4. ☑️ 自动勾选服务条款
5. 👆 **手动点击"确定"按钮**

## 配置参数

在`spis.py`文件顶部可以修改以下配置：

```python
# ========== 配置部分 ==========
CSV_FILE = '导出的条目.csv'          # 原始CSV文件名
XLSX_FILE = '导出的条目.xlsx'        # 转换后的Excel文件名
BATCHED_XLSX_FILE = '带批次_导出的条目.xlsx'  # 分批后的Excel文件名
BATCH_SIZE = 50                      # 每批处理数量
CHROME_DRIVER_PATH = 'chromedriver.exe'     # ChromeDriver路径
WAIT_TIME = 15                       # 页面元素等待时间（秒）
# =============================
```

## API参考

### 核心函数

#### `csv_to_xlsx(csv_file, xlsx_file)`
**功能**: 将CSV文件转换为Excel格式，只保留Title和DOI两列

**参数**:
- `csv_file` (str): 输入CSV文件路径
- `xlsx_file` (str): 输出Excel文件路径

**返回值**: 转换后的Excel文件路径

**示例**:
```python
result_file = csv_to_xlsx('导出的条目.csv', '导出的条目.xlsx')
```

#### `add_order_and_batchN(xlsx_file, batched_xlsx_file)`
**功能**: 为Excel文件添加序号和批次编号列

**参数**:
- `xlsx_file` (str): 输入Excel文件路径
- `batched_xlsx_file` (str): 输出的分批Excel文件路径

**返回值**: `(DataFrame, int)` - 处理后的数据框和总批次数

**示例**:
```python
df, total_batches = add_order_and_batchN('导出的条目.xlsx', '带批次_导出的条目.xlsx')
```

#### `process_batch_xlsx(batch_num)`
**功能**: 处理指定批次的文献DOI

**参数**:
- `batch_num` (str): 要处理的批次编号

**功能流程**:
1. 读取指定批次数据
2. 启动浏览器自动化
3. 逐个处理DOI
4. 输出处理报告

**示例**:
```python
process_batch_xlsx('1')  # 处理第1批
```

## 输出文件说明

### 生成文件
- `导出的条目.xlsx`: 清洗后的Excel文件（仅Title和DOI列）
- `带批次_导出的条目.xlsx`: 添加了order和batchN列的完整文件

### 文件结构
| 列名 | 说明 | 示例 |
|------|------|------|
| order | 顺序编号 | 1, 2, 3... |
| DOI | 文献DOI号 | 10.1016/j.ijhm.2025.104227 |
| batchN | 批次编号 | 1, 1, 1..., 2, 2, 2... |
| Title | 文献标题 | Research Article Title |

## 示例输出

### 分批统计示例
```
共计有DOI信息的文献：88篇
共分为2批
第1批：50篇
第2批：38篇

实际读取到的第1批次所有行内容（Excel表格格式）：
order	DOI	batchN	Title
1	10.1016/j.ijhm.2025.104227	1	Research on Tourism...
2	10.1177/00472875231214743	1	Tourist Destination...
...
```

### 处理进度示例
```
已处理 3/50 篇，当前处理：Tourist Rural Destination Restorative Capacity
已处理 4/50 篇，当前处理：Sustainable Tourism Development in Rural Areas
...
```

### 批次完成报告示例
```
第1批次处理完成！
成功处理：48 篇
失败DOI及原因：
10.1177/00472875231213992 - Tourist Destination Analysis - 页面加载超时
10.1145/3139352 - Digital Tourism Platform - 未找到"文献求助"按钮
总耗时：15分32秒
```

## 故障排除

### 常见问题

#### 1. 语法错误 `SyntaxError: invalid decimal literal`
**原因**: 代码文件开头有多余字符
**解决**: 确保`spis.py`第一行是`import pandas as pd`

#### 2. ChromeDriver版本不匹配
**症状**: 浏览器无法启动或立即关闭
**解决**: 
```bash
# 检查Chrome版本
chrome://version/

# 下载对应版本的ChromeDriver
# 替换项目中的chromedriver.exe
```

#### 3. CSV文件读取错误
**症状**: `错误：原始文件中未找到DOI和Title两列`
**解决**: 
- 检查CSV文件是否包含`Title`和`DOI`列（大小写敏感）
- 确认文件编码为UTF-8
- 验证分隔符是否正确

#### 4. 浏览器元素定位失败
**症状**: `页面加载超时`或`未找到元素`
**解决**: 
- 增加`WAIT_TIME`配置值
- 确保网站正常访问
- 检查网站页面结构是否发生变化

#### 5. 权限错误
**症状**: 无法保存Excel文件
**解决**: 
- 确保运行目录有写入权限
- 关闭Excel文件（如果已打开）
- 以管理员身份运行程序

### 调试模式

启用详细调试信息：
```python
# 在代码中添加调试输出
import logging
logging.basicConfig(level=logging.DEBUG)
```

## 注意事项

### ⚠️ 重要提醒
1. **人工配合**: 需要手动点击"搜文章"、"文献求助"、"确定"按钮
2. **登录要求**: 必须先手动登录SPIS网站
3. **浏览器兼容**: 仅支持Chrome浏览器
4. **网络稳定**: 确保网络连接稳定，避免超时

### 💡 最佳实践
1. **小批量测试**: 首次使用建议先处理少量数据
2. **定期保存**: 大批量处理时建议分批执行
3. **错误记录**: 及时记录失败的DOI，便于后续处理
4. **版本管理**: 定期更新ChromeDriver版本

## 许可证

本项目仅供学术研究使用，请遵守相关网站的使用条款。

## 支持与反馈

如遇到问题或有改进建议，请查看：
1. `task_requirements.md` - 详细技术规范
2. 项目Issues - 常见问题解答
3. 联系开发者获取技术支持

---

**版本信息**: v1.0.0  
**最后更新**: 2024年12月  
**兼容性**: Python 3.7+, Windows 10+ 