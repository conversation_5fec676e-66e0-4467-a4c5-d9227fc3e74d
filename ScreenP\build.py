#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
构建脚本 - 用于将应用程序打包成独立的可执行文件
"""

import os
import sys
import subprocess
import shutil
from pathlib import Path

def build_application():
    """构建应用程序"""
    print("🚀 开始构建AI文献筛选工具...")
    
    # 检查PyInstaller是否安装
    try:
        import PyInstaller
        print(f"✓ PyInstaller版本: {PyInstaller.__version__}")
    except ImportError:
        print("❌ PyInstaller未安装，正在安装...")
        subprocess.run([sys.executable, "-m", "pip", "install", "PyInstaller"], check=True)
    
    # 清理之前的构建
    build_dirs = ["build", "dist", "__pycache__"]
    for dir_name in build_dirs:
        if os.path.exists(dir_name):
            print(f"🧹 清理目录: {dir_name}")
            shutil.rmtree(dir_name)
    
    # PyInstaller构建参数
    build_args = [
        "pyinstaller",
        "--onefile",                    # 打包成单个文件
        "--windowed",                   # Windows下不显示控制台
        "--name=AI文献筛选工具",         # 可执行文件名称
        "--icon=icon.ico",              # 图标文件（如果有）
        "--add-data=README.md;.",       # 包含README文件
        "--hidden-import=PyQt6.QtCore",
        "--hidden-import=PyQt6.QtGui", 
        "--hidden-import=PyQt6.QtWidgets",
        "--hidden-import=pandas",
        "--hidden-import=requests",
        "--hidden-import=openpyxl",
        "--hidden-import=xlrd",
        "--collect-all=PyQt6",
        "main.py"
    ]
    
    # 如果在Windows上，移除图标参数（如果图标文件不存在）
    if not os.path.exists("icon.ico"):
        build_args = [arg for arg in build_args if not arg.startswith("--icon")]
    
    try:
        print("🔨 正在构建...")
        result = subprocess.run(build_args, check=True, capture_output=True, text=True)
        print("✓ 构建成功！")
        
        # 显示输出文件信息
        dist_dir = Path("dist")
        if dist_dir.exists():
            exe_files = list(dist_dir.glob("*.exe")) + list(dist_dir.glob("AI文献筛选工具*"))
            if exe_files:
                exe_file = exe_files[0]
                file_size = exe_file.stat().st_size / (1024 * 1024)  # MB
                print(f"📦 可执行文件: {exe_file}")
                print(f"📏 文件大小: {file_size:.1f} MB")
                print(f"📍 完整路径: {exe_file.absolute()}")
            else:
                print("⚠️  未找到可执行文件")
        
    except subprocess.CalledProcessError as e:
        print(f"❌ 构建失败: {e}")
        print("错误输出:", e.stderr)
        return False
    
    print("\n🎉 构建完成！")
    print("📋 使用说明:")
    print("   1. 可执行文件位于 'dist' 目录中")
    print("   2. 可以将此文件复制到任何Windows电脑上运行")
    print("   3. 无需安装Python或其他依赖")
    
    return True

def create_installer_script():
    """创建安装脚本"""
    installer_content = """@echo off
echo 正在安装AI文献筛选工具...

REM 创建程序目录
if not exist "C:\\Program Files\\AI文献筛选工具" (
    mkdir "C:\\Program Files\\AI文献筛选工具"
)

REM 复制可执行文件
copy "AI文献筛选工具.exe" "C:\\Program Files\\AI文献筛选工具\\AI文献筛选工具.exe"

REM 创建桌面快捷方式
echo Set oWS = WScript.CreateObject("WScript.Shell") > CreateShortcut.vbs
echo sLinkFile = "%USERPROFILE%\\Desktop\\AI文献筛选工具.lnk" >> CreateShortcut.vbs
echo Set oLink = oWS.CreateShortcut(sLinkFile) >> CreateShortcut.vbs
echo oLink.TargetPath = "C:\\Program Files\\AI文献筛选工具\\AI文献筛选工具.exe" >> CreateShortcut.vbs
echo oLink.Save >> CreateShortcut.vbs
cscript CreateShortcut.vbs
del CreateShortcut.vbs

echo 安装完成！桌面已创建快捷方式。
pause
"""
    
    with open("install.bat", "w", encoding="gbk") as f:
        f.write(installer_content)
    
    print("✓ 创建了安装脚本: install.bat")

def main():
    """主函数"""
    print("🔬 AI文献筛选工具 - 构建系统")
    print("=" * 50)
    
    if build_application():
        create_installer_script()
        print("\n✨ 全部完成！现在您可以:")
        print("   • 直接运行 dist/AI文献筛选工具.exe")
        print("   • 或使用 install.bat 进行系统安装")

if __name__ == "__main__":
    main() 