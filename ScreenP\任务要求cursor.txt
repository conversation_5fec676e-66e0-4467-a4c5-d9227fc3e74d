---

我需要你为我构建一个专门用于文献筛选的自动化工具。这个工具的核心功能是让我能够导入一批文献，配置并调用AI模型对它们进行逐篇评估，最后将评估结果整合输出。

### **〇、 总体界面与交互设计原则**

在开始构建具体功能前，请遵循以下设计原则，以确保最终工具的品质：

*   **布局合理，逻辑清晰**：界面布局需遵循自然的工作流程，例如：AI配置区 -> 数据导入与任务设置区 -> 进度监控区。所有元素应排列整齐，不能互相重叠或遮挡。
*   **方便易用，引导明确**：工具必须是用户友好的，所有按钮、下拉菜单和输入框都应有清晰的标签说明。整体操作应直观，减少用户的学习成本。
*   **界面美化，体验舒适**：界面应整洁、美观，运用合理的间距、统一的字体和协调的色彩方案，以提升专业感并避免长时间使用带来的视觉疲劳。

### **一、 AI模型配置面板**

我需要一个集中的面板来配置AI服务。

1.  **平台选择**：界面上需要有三个可供点选的平台服务商：`deepseek`、`chatanywhere` 和 `硅基流动 (SiliconCloud)`。

2.  **平台配置流程**：当我选择其中一个平台后，你需要执行以下操作：
    *   **自动加载API Key**：你必须在后台自动使用我为该平台提供的API密钥。这个过程不需要我进行任何手动输入或确认，API密钥是预设好的。
    *   **模型选择**：提供一个该平台专属的AI模型下拉菜单，并预先选好我指定的默认模型。
    *   **参数调整**：提供两个参数的调整控件，让我可以为选定的模型进行设置。
        *   **Max Tokens**: 范围为 **0** 到 **8192**，默认值为 **4096**。
        *   **Temperature**: 范围为 **0** 到 **2**，默认值为 **0.7**。
        *   这两个参数都需要同时提供**拖动滑块**和**可直接填写的输入框**两种修改方式。

3.  **各平台具体信息**：
    *   **平台一：deepseek**
        *   **API Key (预设)**: `***********************************`
        *   **可选择的模型**: `deepseek-chat`, `deepseek-reasoner`
        *   **默认选中的模型**: `deepseek-chat`
    *   **平台二：chatanywhere**
        *   **API Key (预设)**: `sk-rHjR1JnETK1p6brlIU3kjbmGL6U477fk8ObNtf4NEEKKDd7a`
        *   **可选择的模型**: (请包含我提供的那份从 `o3` 到 `grok-3-deepsearch` 的完整列表)
        *   **默认选中的模型**: `gemini-2.5-flash-preview-04-17`
    *   **平台三：硅基流动 (SiliconCloud)**
        *   **API Key (预设)**: `sk-tqxhxyuplezotnpnpazrnbcqwdyeiyuwgllurnvpkaaaiajn`
        *   **可选择的模型**: (请包含我提供的那份从 `deepseek-ai/DeepSeek-R1-0528-Qwen3-8B` 到 `Pro/deepseek-ai/DeepSeek-R1` 的完整列表)
        *   **默认选中的模型**: `Qwen/Qwen2.5-72B-Instruct-128K`

### **二、 筛选标准提示词（Prompt）与数据流**

1.  **提示词文本框**：我需要一个大的文本框。请将我提供的“【正式版】AI论文筛选任务提示词”的全部内容，从“角色与任务”到“输出格式”，预先完整地填充到这个文本框中，作为给AI的默认系统指令。
2.  **数据处理逻辑**：这个文本框里的内容是固定的“指令部分”。在实际运行时，你需要将导入的文献信息（即“标题”和“摘要”）作为“数据部分”，附加在这段固定指令之后，然后一起发送给AI。每一篇论文都重复这个“指令+数据”的组合过程。

### **三、 数据导入与任务执行**

1.  **文件导入**：提供一个按钮，让我可以导入 `.xlsx`、`.xls` 或 `.csv` 格式的文献文件。程序需识别文件的第一行为列名。
2.  **数据准备**：对于文件中的每一行，你需要读取 `title` 列和 `Abstract Note` 列的内容，并整合成格式为 `title: [标题内容]; abstract: [摘要内容]` 的字符串，用于后续提交给AI。
3.  **运行与中止**：提供一个“**运行AI筛选**”按钮来启动流程，和一个“**中止**”按钮来随时停止所有任务。
4.  **批处理与进度显示**：
    *   **批次设定**：让我可以设定一个**总批次数**（例如，将全部文献分成5批来处理）。程序需要根据文献总量和批次数自动计算出每批应处理的文献数量。
    *   **进度条**：我需要一个**简洁明了**的进度显示区。它应包含一个主进度条来展示总体完成情况，并在下方用**一行简短的文字**清晰地标明当前状态。示例如下：
        *   **主进度条**: `[██████░░░░░░░░░] 40%`
        *   **状态文字**: `总进度: 80/200 | 批次: 2/5 (30/40) | 状态: 正在提交...`

### **四、 结果输出**

1.  **数据提取**：当AI返回处理结果后，你需要从中提取出“**符合度**”的百分比数值和“**判断理由**”后面的全部文字。
2.  **输出文件格式与内容**：
    *   最终的输出文件**总共包含5列**。
    *   **列的顺序和名称**如下：
        1.  `序号`：从1开始的数字。
        2.  `title`：从导入文件 `title` 列获取的内容。
        3.  `Abstract Note`：从导入文件 `Abstract Note` 列获取的内容。
        4.  `符合度`：从AI结果中提取的百分比。
        5.  `判断理由`：从AI结果中提取的理由全文。
    *   **文件保存**：
        *   **位置**：保存在与导入文件**相同的文件夹**中。
        *   **格式**：与导入文件**格式相同**（.xlsx, .xls, 或 .csv）。
        *   **命名**：在原始导入文件名后**加上“-R”**。例如，如果导入的是 `papers.xlsx`，则输出文件名为 `papers-R.xlsx`。