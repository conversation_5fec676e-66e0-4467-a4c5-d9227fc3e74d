# AI文献筛选自动化工具 - 任务需求文档

## 1. 项目概述

### 1.1 项目背景
开发一个基于人工智能的文献筛选自动化工具，用于帮助学术研究人员快速筛选和评估大量学术论文。该工具需要支持多种AI平台，能够根据用户自定义的筛选标准自动分析论文标题和摘要，并给出详细的符合度评估。

### 1.2 项目目标
- 提高文献筛选效率，减少人工筛选工作量
- 支持大规模文献数据的批量处理
- 提供可视化的操作界面和实时进度监控
- 确保处理过程的稳定性和可恢复性
- 支持多种文件格式和编码标准

### 1.3 目标用户
- 学术研究人员
- 文献综述撰写者
- 科研项目团队
- 学位论文撰写者

## 2. 功能需求

### 2.1 AI平台集成
#### 2.1.1 支持的AI平台
- **DeepSeek平台**
  - API端点：https://api.deepseek.com/v1/chat/completions
  - 支持模型：deepseek-chat, deepseek-reasoner
  - 默认模型：deepseek-chat

- **ChatAnywhere平台**
  - API端点：https://api.chatanywhere.tech/v1/chat/completions
  - 支持70+种先进AI模型
  - 默认模型：gemini-2.5-flash-preview-04-17

#### 2.1.2 AI参数配置
- **Max Tokens**：0-8192，控制AI回复的最大长度
- **Temperature**：0.0-2.0，控制AI回复的创造性程度
- **API Key管理**：预设密钥 + 用户自定义替换功能

#### 2.1.3 API调用优化
- 请求超时设置：60秒
- 自动重试机制：最多3次重试
- 重试延时：2秒、5秒、10秒递增
- 调用间隔：每次API调用间隔0.5秒

### 2.2 数据处理功能
#### 2.2.1 文件格式支持
- **Excel格式**：.xlsx, .xls
- **CSV格式**：.csv
- **编码支持**：UTF-8, UTF-8-BOM, GBK, GB2312, Latin1, CP1252

#### 2.2.2 数据清洗规则
- 自动识别标题列：title, 标题, titles, paper title, article title
- 自动识别摘要列：abstract note, abstract, 摘要, abstracts, note, abstract notes, summary
- 过滤空值记录：标题或摘要为空的记录
- 过滤空字符串：仅包含空白字符的记录

#### 2.2.3 数据验证
- 文件完整性检查
- 列名匹配验证
- 数据类型验证
- 记录数量统计

### 2.3 分段处理功能
#### 2.3.1 范围设置
- **起始行设置**：1-999999，指定处理起始位置
- **结束行设置**：1-999999，指定处理结束位置
- **全部行快速设置**：一键设置处理全部数据
- **范围有效性验证**：实时检查范围合理性

#### 2.3.2 批次管理
- **批次数配置**：1-50批次可选
- **智能批次计算**：根据选定范围自动计算每批处理量
- **不均匀批次处理**：最后一批自动包含剩余记录

#### 2.3.3 文件命名规范
- **测试文件**：`原文件名-TEST-R起始行-结束行.扩展名`
- **正式结果**：`原文件名-R起始行-结束行.扩展名`
- **序号保持**：输出文件中的序号列显示原始文件实际行号

### 2.4 评估标准配置
#### 2.4.1 提示词系统
- **默认模板**：内置"AI决策与责任归因"专业筛选标准
- **自定义编辑**：支持用户完全自定义筛选标准
- **提示词长度**：无限制，支持复杂评估要求

#### 2.4.2 评估输出格式
AI需要按照以下格式输出评估结果：
```
符合度评估结果：[0%-100%]%

判断理由：
1. 主题A（AI决策）相关性分析：[详细分析]
2. 主题B（责任）相关性分析：[详细分析]  
3. 综合评估与打分逻辑：[详细说明]
```

### 2.5 用户界面需求
#### 2.5.1 界面布局
- **窗口尺寸**：1200x1000像素，支持最大化
- **滚动支持**：内容超出时自动显示滚动条
- **响应式设计**：适配不同屏幕尺寸

#### 2.5.2 交互控件
- **平台选择**：下拉菜单选择AI平台
- **模型选择**：下拉菜单选择具体模型
- **参数调节**：滑块 + 输入框双向绑定
- **文件导入**：文件选择对话框
- **范围设置**：数字输入框 + 快速设置按钮

#### 2.5.3 进度监控
- **进度条显示**：当前处理进度百分比
- **状态标签**：实时显示处理状态
- **详细日志**：显示每条记录的处理详情
- **错误报告**：显示失败记录和错误原因

### 2.6 运行模式
#### 2.6.1 测试模式
- **测试范围**：处理前5条记录
- **配置验证**：验证AI配置和提示词设置
- **输出检查**：检查AI输出格式是否符合预期
- **快速反馈**：快速验证系统配置正确性

#### 2.6.2 正式模式
- **批量处理**：处理指定范围内的所有记录
- **实时保存**：每处理一条记录立即保存到文件
- **中断恢复**：支持处理中断后从断点继续
- **完成通知**：处理完成后自动打开结果文件

## 3. 技术规范

### 3.1 开发环境
- **编程语言**：Python 3.8+
- **GUI框架**：PyQt6
- **数据处理**：pandas
- **HTTP请求**：requests
- **文件处理**：openpyxl

### 3.2 架构设计
#### 3.2.1 主要模块
- **AILiteratureScreeningTool**：主窗口类，负责UI管理
- **ProgressWorker**：工作线程类，负责AI调用和数据处理
- **AI_PLATFORMS**：AI平台配置字典
- **DEFAULT_PROMPT**：默认提示词常量

#### 3.2.2 线程设计
- **主线程**：UI交互和界面更新
- **工作线程**：AI API调用和文件操作
- **信号机制**：progress_updated, result_ready, error_occurred, finished等

#### 3.2.3 错误处理
- **API错误**：HTTP状态码错误处理
- **网络错误**：连接超时和网络异常处理
- **文件错误**：文件读写权限和格式错误处理
- **数据错误**：数据格式和内容验证错误处理

### 3.3 性能要求
#### 3.3.1 处理速度
- **单条记录处理时间**：≤ 2秒（包含0.5秒延时）
- **并发限制**：单线程顺序处理，避免API频率限制
- **内存使用**：≤ 2GB RAM

#### 3.3.2 稳定性要求
- **连续运行时间**：支持连续运行8小时以上
- **错误恢复**：单条记录失败不影响后续处理
- **数据完整性**：确保处理结果不丢失

### 3.4 兼容性要求
#### 3.4.1 操作系统支持
- **Windows**：Windows 10/11
- **macOS**：macOS 10.14+
- **Linux**：Ubuntu 18.04+

#### 3.4.2 Python环境
- **Python版本**：3.8, 3.9, 3.10, 3.11
- **依赖库版本**：使用requirements.txt固定版本

## 4. 输入输出规范

### 4.1 输入要求
#### 4.1.1 文献文件格式
- **文件类型**：Excel (.xlsx, .xls) 或 CSV (.csv)
- **必需列**：
  - 标题列：包含论文标题的完整信息
  - 摘要列：包含论文摘要的完整信息
- **数据质量**：标题和摘要不能为空或仅包含空白字符

#### 4.1.2 配置参数
- **AI平台**：必须选择有效的AI平台
- **模型选择**：必须选择平台支持的模型
- **API Key**：使用预设或提供有效的自定义密钥
- **提示词**：必须提供非空的筛选标准描述

#### 4.1.3 处理范围
- **起始行**：≥ 1 且 ≤ 文件总行数
- **结束行**：≥ 起始行 且 ≤ 文件总行数
- **批次数**：1-50之间的整数

### 4.2 输出规范
#### 4.2.1 结果文件结构
| 列名 | 数据类型 | 说明 |
|------|----------|------|
| 序号 | 整数 | 原始文件中的实际行号 |
| title | 文本 | 论文标题原文 |
| Abstract Note | 文本 | 论文摘要原文 |
| 符合度&判断理由 | 文本 | AI完整评估结果 |

#### 4.2.2 文件命名规则
- **测试结果**：`[原文件名]-TEST-R[起始行]-[结束行].[扩展名]`
- **正式结果**：`[原文件名]-R[起始行]-[结束行].[扩展名]`
- **编码格式**：UTF-8 with BOM（CSV）或默认编码（Excel）

#### 4.2.3 处理日志
- **控制台输出**：处理进度和错误信息
- **GUI日志框**：详细的处理过程信息
- **状态显示**：实时更新的处理状态

## 5. 验收标准

### 5.1 功能验收
#### 5.1.1 基础功能
- [ ] AI平台配置和模型选择正常工作
- [ ] 文件导入支持Excel和CSV格式
- [ ] 数据清洗正确识别和处理无效记录
- [ ] 提示词编辑和应用功能正常

#### 5.1.2 分段处理功能
- [ ] 范围设置和验证功能正常
- [ ] 批次计算和分配正确
- [ ] 文件命名包含正确的范围信息
- [ ] 序号列显示正确的原始行号

#### 5.1.3 AI评估功能
- [ ] API调用成功率 > 95%
- [ ] 重试机制在网络异常时正常工作
- [ ] AI评估结果格式正确
- [ ] 错误记录正确标注失败原因

#### 5.1.4 界面交互
- [ ] 所有控件响应正常
- [ ] 进度条和状态显示准确
- [ ] 错误提示清晰易懂
- [ ] 文件自动打开功能正常

### 5.2 性能验收
#### 5.2.1 处理效率
- [ ] 单条记录处理时间 ≤ 2秒
- [ ] 1000条记录处理时间 ≤ 45分钟
- [ ] 内存使用稳定，无内存泄漏

#### 5.2.2 稳定性测试
- [ ] 连续处理2000条记录无崩溃
- [ ] 网络中断后自动重试成功
- [ ] 文件权限错误时提供明确错误信息

### 5.3 兼容性验收
#### 5.3.1 文件格式兼容
- [ ] 支持多种Excel版本(.xls, .xlsx)
- [ ] 支持多种CSV编码格式
- [ ] 正确识别不同语言的列名

#### 5.3.2 系统兼容
- [ ] Windows 10/11正常运行
- [ ] 不同屏幕分辨率下界面正常显示
- [ ] 支持高DPI显示器

### 5.4 用户体验验收
#### 5.4.1 易用性
- [ ] 新用户5分钟内能完成首次使用
- [ ] 默认配置适合大多数使用场景
- [ ] 错误提示包含具体的解决建议

#### 5.4.2 可靠性
- [ ] 处理中断不丢失已完成的结果
- [ ] 结果文件可以被Excel正常打开
- [ ] 分段处理结果可以正确合并

## 6. 部署和维护

### 6.1 部署要求
#### 6.1.1 环境准备
```bash
# Python环境
python >= 3.8

# 依赖安装
pip install PyQt6 pandas requests openpyxl

# 可选：打包为独立可执行文件
pip install pyinstaller
```

#### 6.1.2 配置检查
- 验证API密钥有效性
- 测试网络连接稳定性
- 确认文件读写权限

### 6.2 维护计划
#### 6.2.1 定期维护
- **API密钥更新**：根据平台要求定期更新
- **模型列表更新**：新增支持的AI模型
- **依赖库更新**：保持依赖库版本安全性

#### 6.2.2 监控指标
- **API调用成功率**：监控各平台API稳定性
- **处理速度变化**：监控处理效率趋势
- **错误类型统计**：分析常见错误原因

### 6.3 扩展规划
#### 6.3.1 功能扩展
- 支持更多AI平台（如OpenAI、Claude等）
- 增加结果统计和可视化功能
- 支持多语言界面
- 增加批量文件处理功能

#### 6.3.2 性能优化
- 实现并发API调用（在API限制允许范围内）
- 优化大文件内存使用
- 增加本地缓存机制
- 实现增量处理功能

---

**文档版本**：v1.1.0  
**最后更新**：2024年  
**维护者**：项目开发团队 