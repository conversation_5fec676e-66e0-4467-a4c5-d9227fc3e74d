#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
配置文件 - 应用程序设置和常量
"""

import os
from pathlib import Path

# 应用程序信息
APP_NAME = "AI文献筛选自动化工具"
APP_VERSION = "1.0.0"
APP_AUTHOR = "AI Assistant"
APP_DESCRIPTION = "专业的AI文献筛选桌面工具"

# 文件和路径配置
PROJECT_ROOT = Path(__file__).parent
CONFIG_DIR = PROJECT_ROOT / "config"
TEMP_DIR = PROJECT_ROOT / "temp"
LOG_DIR = PROJECT_ROOT / "logs"

# 确保目录存在
for directory in [CONFIG_DIR, TEMP_DIR, LOG_DIR]:
    directory.mkdir(exist_ok=True)

# 支持的文件格式
SUPPORTED_FILE_FORMATS = {
    "Excel文件": "*.xlsx *.xls",
    "CSV文件": "*.csv",
    "所有支持的文件": "*.xlsx *.xls *.csv"
}

# 必需的数据列
REQUIRED_COLUMNS = ["title", "Abstract Note"]

# API配置
API_TIMEOUT = 60  # 秒
API_RETRY_COUNT = 3
API_RETRY_DELAY = 1  # 秒

# UI配置
WINDOW_MIN_WIDTH = 1000
WINDOW_MIN_HEIGHT = 700
PROGRESS_UPDATE_INTERVAL = 100  # 毫秒

# 默认参数
DEFAULT_MAX_TOKENS = 4096
DEFAULT_TEMPERATURE = 0.7
DEFAULT_BATCH_COUNT = 5

# 日志配置
LOG_FORMAT = "%(asctime)s - %(name)s - %(levelname)s - %(message)s"
LOG_LEVEL = "INFO"

# 样式主题颜色
THEME_COLORS = {
    "primary": "#3498db",
    "secondary": "#2980b9", 
    "success": "#27ae60",
    "warning": "#f39c12",
    "danger": "#e74c3c",
    "light": "#ecf0f1",
    "dark": "#2c3e50",
    "muted": "#7f8c8d"
}

# 字体配置
FONT_FAMILY = "Microsoft YaHei, Arial, sans-serif"
FONT_SIZE_SMALL = 12
FONT_SIZE_NORMAL = 14
FONT_SIZE_LARGE = 16
FONT_SIZE_TITLE = 24 