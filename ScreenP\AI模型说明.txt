需要你调用的下述三个平台的AI服务详细信息如下：

调用各AI模型的平台服务名（如deepseek等）可点选，一旦确定调用某个平台后，就需要你对下面的信息进行处理：
其一，输入API KEY,我需要你默认输入我提供给你的各个平台的API密钥，既尽管留空，但不要让我手动填写。
其二，我会提供此服务平台下包含的多个AI模型（供我选择），我需要你提供模型名称(允许下拉选择，同时也先填入默认值）。
其三，一旦确定选择哪个模型后，我需要你针对我选择的模型，设置其max tokens（0到8192，默认4096）和temperature（0到2，默认0.7）.我需要可以通过拖动条来进行更改，同时也可以直接在方框中填写相应的数值。
第一，deepseek
API key：***********************************
可供选择的模型名为：
deepseek-chat
deepseek-reasoner
默认选择的模型为：
deepseek-chat

第二，chatanywhere
API key：sk-rHjR1JnETK1p6brlIU3kjbmGL6U477fk8ObNtf4NEEKKDd7a
可供选择的模型名为：
o3
o3-2025-04-16
o4-mini
o4-mini-2025-04-16
gpt-4.1
gpt-4.1-2025-04-14
gpt-4.1-mini
gpt-4.1-mini-2025-04-14
gpt-4.1-nano
gpt-4.1-nano-2025-04-14
gpt-3.5-turbo
gpt-3.5-turbo-1106
gpt-3.5-turbo-0125
gpt-3.5-turbo-16k
gpt-3.5-turbo-instruct
gpt-4.5-preview
gpt-4.5-preview-2025-02-27
o1-mini
o1-preview
o3-mini [5]
o1 [5]
gpt-4o-search-preview
gpt-4o-search-preview-2025-03-11
gpt-4o-mini-search-preview
gpt-4o-mini-search-preview-2025-03-11
gpt-4
gpt-4o
gpt-4o-2024-05-13
gpt-4o-2024-08-06
gpt-4o-2024-11-20
chatgpt-4o-latest
gpt-4o-mini
gpt-4-0613
gpt-4-turbo-preview
gpt-4-0125-preview
gpt-4-1106-preview
gpt-4-vision-preview
gpt-4-turbo
gpt-4-turbo-2024-04-09
gpt-3.5-turbo-ca
gpt-4-ca
gpt-4-turbo-ca
gpt-4o-ca
gpt-4o-mini-ca
chatgpt-4o-latest-ca
o1-mini-ca
o1-preview-ca
deepseek-reasoner
deepseek-r1
deepseek-v3
claude-3-7-sonnet-20250219
claude-3-5-sonnet-20240620
claude-3-5-sonnet-20241022
claude-3-5-haiku-20241022
gemini-1.5-flash-latest
gemini-1.5-pro-latest
gemini-exp-1206
gemini-2.0-flash-exp
gemini-2.0-pro-exp-02-05
gemini-2.0-flash
gemini-2.5-pro-exp-03-25
gemini-2.5-pro-preview-05-06
gemini-2.5-flash-preview-04-17
grok-3
grok-3-reasoner
grok-3-deepsearch
默认选择的模型是：
gemini-2.5-flash-preview-04-17

第三，硅基流动（SiliconCloud）
API key：sk-tqxhxyuplezotnpnpazrnbcqwdyeiyuwgllurnvpkaaaiajn
deepseek-ai/DeepSeek-R1-0528-Qwen3-8B
THUDM/GLM-Z1-9B-0414
deepseek-ai/DeepSeek-R1-Distill-Qwen-7B
Qwen/Qwen2.5-7B-Instruct
Qwen/Qwen2.5-Coder-7B-Instruct
THUDM/GLM-4-9B-0414
Qwen/Qwen3-8B
internlm/internlm2_5-7b-chat
THUDM/glm-4-9b-chat
Qwen/Qwen2-7B-Instruct
Pro/deepseek-ai/DeepSeek-R1-Distill-Qwen-7B
Pro/Qwen/Qwen2.5-Coder-7B-Instruct
Pro/Qwen/Qwen2.5-VL-7B-Instruct
Pro/Qwen/Qwen2.5-7B-Instruct
Pro/Qwen/Qwen2-7B-Instruct
Pro/THUDM/glm-4-9b-chat
deepseek-ai/DeepSeek-R1-Distill-Qwen-14B
Qwen/Qwen2.5-14B-Instruct
deepseek-ai/deepseek-vl2
deepseek-ai/DeepSeek-R1-Distill-Qwen-32B
Qwen/Qwen2.5-32B-Instruct
Qwen/Qwen2.5-Coder-32B-Instruct
Qwen/QwQ-32B-Preview
deepseek-ai/DeepSeek-V2.5
Qwen/Qwen2.5-VL-32B-Instruct
THUDM/GLM-4-32B-0414
Qwen/Qwen3-14B
Qwen/Qwen3-30B-A3B
Tongyi-Zhiwen/QwenLong-L1-32B
THUDM/GLM-Z1-Rumination-32B-0414
THUDM/GLM-Z1-32B-0414
Qwen/QwQ-32B
Qwen/Qwen3-32B
Qwen/Qwen2.5-72B-Instruct
Qwen/Qwen2.5-VL-72B-Instruct
Qwen/Qwen2.5-72B-Instruct-128K
Qwen/Qwen2-VL-72B-Instruct
deepseek-ai/DeepSeek-V3
Pro/deepseek-ai/DeepSeek-V3-1226
Pro/deepseek-ai/DeepSeek-V3
Qwen/QVQ-72B-Preview
Qwen/Qwen3-235B-A22B
Pro/deepseek-ai/DeepSeek-R1-0120
deepseek-ai/DeepSeek-R1
Pro/deepseek-ai/DeepSeek-R1
默认选择的模型为：
Qwen/Qwen2.5-72B-Instruct-128K