@echo off
title AI文献筛选工具
echo.
echo ========================================
echo        🔬 AI文献筛选工具
echo ========================================
echo.

REM 检查Python是否安装
python --version >nul 2>&1
if errorlevel 1 (
    echo ❌ 未检测到Python，请先安装Python 3.8或更高版本
    echo 下载地址: https://www.python.org/downloads/
    pause
    exit /b 1
)

echo ✓ 检测到Python环境
echo.

REM 运行启动脚本
echo 🚀 正在启动应用程序...
python run.py

pause 