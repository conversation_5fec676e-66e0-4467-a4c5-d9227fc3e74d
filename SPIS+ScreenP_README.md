# SPIS+ScreenP 整合工具 v1.1

[![Python Version](https://img.shields.io/badge/python-3.7+-blue.svg)](https://python.org)
[![License](https://img.shields.io/badge/license-MIT-green.svg)](LICENSE)
[![Platform](https://img.shields.io/badge/platform-Windows-lightgrey.svg)](https://www.microsoft.com/windows)
[![Version](https://img.shields.io/badge/version-1.1-brightgreen.svg)](https://github.com)

## 📖 项目概述

SPIS+ScreenP 是一个集成了两个独立学术研究工具的桌面应用程序，专为学术研究人员设计，提供文献求助自动化和AI文献筛选功能。

### 🎯 核心功能

**双工具集成架构**：
- 🔴 **SPIS文献求助工具** - 自动化学术文献求助流程，支持可编辑批次管理
- 🔵 **ScreenP AI文献筛选工具** - 基于AI的智能文献筛选

**同时启动，独立运行**：
- 程序启动后同时显示两个独立的图形界面窗口
- 两个工具完全独立，可以同时使用，互不干扰
- 每个工具保持原有的完整功能和内部逻辑

### ✨ v1.1 新功能亮点

**智能批次管理**：
- 🔄 **自动浏览器切换** - 批次切换时自动管理浏览器窗口
- 📝 **可编辑批次详情** - Excel式数据编辑功能
- 🪟 **非模态窗口设计** - 多窗口同时操作，提升工作效率
- ⚡ **响应性优化** - 按钮响应更加灵敏

## 🚀 快速开始

### 系统要求

- **操作系统**: Windows 10/11
- **Python版本**: 3.7+
- **浏览器**: Google Chrome (用于SPIS工具)
- **内存**: 建议4GB以上
- **网络**: 稳定的互联网连接

### 安装步骤

1. **克隆或下载项目**
   ```bash
   git clone <repository-url>
   cd SPIS+ScreenP
   ```

2. **安装依赖库**
   ```bash
   pip install pandas selenium requests PyQt6 openpyxl
   ```

3. **下载Chrome驱动**
   - 下载与您的Chrome版本匹配的chromedriver.exe
   - 将chromedriver.exe放置在项目根目录

4. **运行程序**
   ```bash
   python "SPIS+ScreenP.py"
   ```

## 🔧 工具详解

### 工具1：SPIS文献求助自动化工具 (红色主题)

#### 功能特点
- **图形化界面操作**，直观便捷
- **自动文件处理**：CSV转Excel，数据清洗
- **智能批次管理**：自动分批，每批50条记录
- **🔄 智能浏览器切换**：批次切换时自动管理浏览器窗口
- **📝 可编辑批次详情**：Excel式数据编辑，支持增删改查
- **🪟 非模态窗口设计**：多窗口同时操作，提升效率
- **浏览器自动化**：Selenium控制Chrome浏览器
- **半自动化流程**：关键步骤需要人工确认
- **实时进度监控**：详细的处理日志和进度显示
- **⚡ 响应性优化**：按钮响应更加灵敏

#### 核心流程
1. **文件处理阶段**
   - 选择包含DOI和Title列的CSV文件
   - 自动转换为Excel格式并清洗数据
   - 生成批次信息，每批50条记录

2. **批次管理阶段** ✨ 新功能
   - 智能批次选择和切换
   - 可编辑批次详情窗口（非模态）
   - Excel式数据编辑：增删改查
   - 实时数据保存和状态提示

3. **浏览器控制阶段**
   - 自动启动Chrome浏览器
   - 打开SPIS网站 (https://spis.hnlat.com)
   - 用户手动登录账户
   - 🔄 批次切换时自动关闭并重新打开浏览器

4. **自动化处理阶段**
   - 自动填写DOI到搜索框
   - 用户手动点击"搜文章"和"文献求助"按钮
   - 自动勾选服务条款
   - 用户手动点击"确定"按钮

#### 技术架构
```python
# 主要类和函数
class SPISLiteratureRequestTool(QMainWindow):
    - init_ui()              # 初始化图形界面
    - create_file_panel()    # 文件处理面板
    - create_batch_panel()   # 批次管理面板
    - create_browser_panel() # 浏览器控制面板
    - process_csv_file()     # 处理CSV文件
    - open_browser()         # 启动浏览器
    - start_batch_processing() # 开始批次处理
    - show_batch_info()      # 显示可编辑批次详情 ✨ 新功能
    - on_batch_changed()     # 批次切换处理 ✨ 新功能

# ✨ 新增：可编辑批次详情窗口类
class EditableBatchDetailWindow(QMainWindow):
    - init_ui()              # 初始化可编辑界面
    - create_editable_table() # 创建可编辑表格
    - create_button_bar()    # 创建操作按钮栏
    - add_row()              # 添加新行
    - delete_selected_rows() # 删除选中行
    - save_changes()         # 保存更改到Excel
    - reset_data()           # 重置到原始数据

# 核心处理函数
def spis_csv_to_xlsx()       # CSV转Excel
def spis_add_order_and_batchN() # 添加序号和批次
def spis_process_batch_xlsx()   # 处理指定批次
```

### 工具2：ScreenP AI文献筛选工具 (蓝色主题)

#### 功能特点
- **多AI平台支持**：DeepSeek、ChatAnywhere等
- **智能文献筛选**：基于自定义提示词进行评估
- **批量处理能力**：支持大规模文献数据处理
- **实时保存机制**：每处理一条记录立即保存
- **灵活范围设置**：可指定处理的行数范围
- **测试运行模式**：处理前5条记录验证配置

#### 核心流程
1. **AI模型配置**
   - 选择AI平台和具体模型
   - 配置API密钥和参数
   - 设置Max Tokens和Temperature

2. **提示词设置**
   - 使用预设的专业提示词模板
   - 支持自定义筛选标准
   - 针对"AI决策与责任归因"主题优化

3. **数据导入处理**
   - 支持Excel和CSV文件格式
   - 自动识别title和Abstract Note列
   - 智能数据清洗和验证

4. **批量筛选执行**
   - 设置处理范围和批次数
   - 选择测试运行或正式运行
   - 实时显示AI评估结果

#### 技术架构
```python
# 主要类和函数
class AILiteratureScreeningTool(QMainWindow):
    - init_ui()                    # 初始化界面
    - create_ai_config_panel()     # AI配置面板
    - create_prompt_panel()        # 提示词面板
    - create_data_panel()          # 数据导入面板
    - import_file()                # 导入文献文件
    - run_screening()              # 执行筛选
    - export_results()             # 导出结果

class ProgressWorker(QObject):
    - run()                        # 执行AI筛选任务
    - call_ai_api()               # 调用AI API
    - parse_ai_response()         # 解析AI响应
```

## 📊 配置参数

### SPIS工具配置
```python
SPIS_BATCH_SIZE = 50              # 每批处理的记录数
SPIS_CHROME_DRIVER_PATH = 'chromedriver.exe'  # Chrome驱动路径
SPIS_WAIT_TIME = 15               # 页面元素等待时间(秒)
```

### ScreenP工具配置
```python
# AI平台配置
AI_PLATFORMS = {
    "deepseek": {
        "name": "DeepSeek",
        "api_key": "your-api-key",
        "base_url": "https://api.deepseek.com/v1/chat/completions",
        "models": ["deepseek-chat", "deepseek-reasoner"],
        "default_model": "deepseek-chat"
    },
    "chatanywhere": {
        "name": "ChatAnywhere",
        "api_key": "your-api-key", 
        "base_url": "https://api.chatanywhere.tech/v1/chat/completions",
        "models": ["gemini-2.5-flash-preview-04-17", ...],
        "default_model": "gemini-2.5-flash-preview-04-17"
    }
}

# 默认参数
DEFAULT_MAX_TOKENS = 4096
DEFAULT_TEMPERATURE = 0.7
DEFAULT_BATCH_COUNT = 5
```

## 📁 文件结构

```
SPIS+ScreenP/
├── SPIS+ScreenP.py              # 主程序文件
├── chromedriver.exe             # Chrome驱动程序
├── 导出的条目.csv               # SPIS工具输入文件
├── 导出的条目.xlsx              # 转换后的Excel文件
├── 带批次_导出的条目.xlsx       # 添加批次信息的文件
├── 筛选结果_YYYYMMDD_HHMMSS.xlsx # ScreenP工具输出文件
├── SPIS+ScreenP_README.md       # 项目文档
├── SPIS+ScreenP_task_requirements.md # 任务需求文档
└── SPIS+ScreenP_user_guide.md   # 用户使用手册
```

## 🔍 API参考

### SPIS工具核心API

#### 文件处理函数
```python
def spis_csv_to_xlsx(csv_file: str, xlsx_file: str) -> str:
    """
    将CSV文件转换为Excel格式

    Args:
        csv_file: 输入CSV文件路径
        xlsx_file: 输出Excel文件路径

    Returns:
        str: 转换后的Excel文件路径
    """

def spis_add_order_and_batchN(xlsx_file: str, batched_xlsx_file: str) -> tuple:
    """
    添加序号和批次信息

    Args:
        xlsx_file: 输入Excel文件路径
        batched_xlsx_file: 输出带批次信息的Excel文件路径

    Returns:
        tuple: (DataFrame对象, 批次总数)
    """
```

#### 浏览器控制函数
```python
def spis_process_batch_xlsx(batch_num: str) -> None:
    """
    处理指定批次的文献求助

    Args:
        batch_num: 批次编号(字符串)
    """
```

#### ✨ 新增：可编辑批次详情窗口API
```python
class EditableBatchDetailWindow(QMainWindow):
    """可编辑的批次详情窗口类"""

    def __init__(self, batch_num: int, batch_data: pd.DataFrame, parent=None):
        """
        初始化可编辑批次详情窗口

        Args:
            batch_num: 批次编号
            batch_data: 批次数据DataFrame
            parent: 父窗口对象
        """

    def add_row(self) -> None:
        """在表格末尾添加新行"""

    def delete_selected_rows(self) -> None:
        """删除选中的行"""

    def save_changes(self) -> None:
        """保存更改到Excel文件"""

    def reset_data(self) -> None:
        """重置数据到原始状态"""
```

#### ✨ 新增：智能批次管理API
```python
class SPISLiteratureRequestTool(QMainWindow):
    """SPIS工具主窗口类"""

    def on_batch_changed(self) -> None:
        """
        批次选择改变时的处理
        - 自动关闭当前浏览器
        - 重置按钮状态
        - 显示切换提示
        """

    def show_batch_info(self) -> None:
        """
        显示批次详情 - 非模态可编辑窗口
        - 防止重复打开同一批次窗口
        - 创建可编辑的详情窗口
        - 管理窗口引用
        """
```

### ScreenP工具核心API

#### 工作线程类
```python
class ProgressWorker(QObject):
    # 信号定义
    progress_updated = pyqtSignal(int, int, int, int, str, int)
    result_ready = pyqtSignal(dict)
    error_occurred = pyqtSignal(str)
    finished = pyqtSignal()
    
    def setup(self, data, platform_config, prompt, batch_count, max_tokens, temperature):
        """设置工作参数"""
    
    def call_ai_api(self, row_data) -> dict:
        """调用AI API进行评估"""
```

## 💡 使用示例

### 示例1：SPIS工具批量处理文献（含新功能）
```python
# 1. 准备CSV文件（包含DOI和Title列）
csv_data = """
DOI,Title
10.1016/j.example.2023.001,Example Paper Title 1
10.1016/j.example.2023.002,Example Paper Title 2
"""

# 2. 运行程序
python "SPIS+ScreenP.py"

# 3. 在SPIS工具窗口中：
#    - 选择CSV文件
#    - 处理文件生成批次
#    - 查看和编辑批次详情（✨ 新功能）
#    - 打开浏览器并登录
#    - 开始自动化处理
#    - 切换批次时自动管理浏览器（✨ 新功能）
```

### 示例2：使用可编辑批次详情窗口
```python
# 1. 在SPIS工具中处理文件生成批次后
# 2. 点击"📋 查看批次详情"按钮
# 3. 在弹出的可编辑窗口中：

# 编辑现有数据
# - 双击任意单元格开始编辑
# - 修改DOI或Title信息
# - 状态栏显示"数据已修改"

# 添加新记录
# - 点击"➕ 添加行"按钮
# - 在新行中填写数据
# - 系统自动设置序号和批次号

# 删除记录
# - 选择要删除的行
# - 点击"➖ 删除选中行"按钮
# - 确认删除操作

# 保存更改
# - 点击"💾 保存更改"按钮
# - 数据保存到Excel文件

# 4. 主窗口功能仍可正常使用（非模态设计）
```

### 示例2：ScreenP工具AI筛选
```python
# 1. 准备文献数据文件（包含title和Abstract Note列）
literature_data = """
title,Abstract Note
"AI Decision Making in Healthcare","This paper explores the use of artificial intelligence..."
"Responsibility Attribution in Autonomous Systems","This study examines how responsibility can be attributed..."
"""

# 2. 运行程序
python "SPIS+ScreenP.py"

# 3. 在ScreenP工具窗口中：
#    - 配置AI模型和参数
#    - 导入文献文件
#    - 设置处理范围
#    - 执行筛选并导出结果
```

## 🛠️ 故障排除

### 常见问题

1. **Chrome驱动问题**
   ```
   错误：chromedriver.exe not found
   解决：下载匹配Chrome版本的驱动程序
   ```

2. **API调用失败**
   ```
   错误：API key invalid
   解决：检查API密钥是否正确和有效
   ```

3. **文件编码问题**
   ```
   错误：UnicodeDecodeError
   解决：程序会自动尝试多种编码格式
   ```

4. **依赖库缺失**
   ```
   错误：ModuleNotFoundError
   解决：pip install pandas selenium requests PyQt6 openpyxl
   ```

5. **✨ 批次详情窗口问题**
   ```
   问题：批次详情窗口无法编辑
   解决：确保双击单元格激活编辑模式

   问题：保存更改失败
   解决：检查Excel文件是否被其他程序占用

   问题：多个批次窗口冲突
   解决：程序自动防止重复打开，点击会激活已有窗口
   ```

6. **✨ 浏览器自动切换问题**
   ```
   问题：切换批次后浏览器没有自动关闭
   解决：检查是否有多个Chrome进程，手动关闭后重试

   问题：浏览器关闭后无法重新打开
   解决：等待几秒钟后再点击"打开浏览器"按钮
   ```

### 性能优化建议

- **内存使用**：处理大文件时建议分批处理
- **网络稳定**：确保稳定的网络连接
- **API限制**：注意AI平台的调用频率限制
- **浏览器资源**：及时关闭不需要的浏览器标签页

## 📄 许可证

本项目采用 MIT 许可证 - 详见 [LICENSE](LICENSE) 文件

## 🤝 贡献指南

欢迎提交问题报告和功能请求！

## 📞 技术支持

如需技术支持，请：
1. 检查本文档的故障排除部分
2. 确认所有依赖库已正确安装
3. 验证网络连接和API配置
4. 查看程序日志获取详细错误信息

## 📈 版本更新日志

### v1.1 (2025年1月) - 重大功能更新
- ✨ **新增**：批次切换时自动浏览器管理
- ✨ **新增**：可编辑的非模态批次详情窗口
- ✨ **新增**：Excel式数据编辑功能（增删改查）
- ⚡ **优化**：按钮响应灵敏度大幅提升
- 🔧 **改进**：多窗口管理和用户体验优化

### v1.0 (2025年1月) - 初始版本
- 🔴 SPIS文献求助自动化工具
- 🔵 ScreenP AI文献筛选工具
- 🪟 双工具集成架构

---

**当前版本**: v1.1
**更新日期**: 2025年1月
**兼容性**: Python 3.7+, Windows 10+
**主要改进**: 可编辑批次管理、智能浏览器切换、非模态窗口设计
