import pandas as pd
import time
from selenium import webdriver
from selenium.webdriver.common.by import By

from selenium.webdriver.chrome.service import Service
from selenium.webdriver.support.ui import WebDriverWait
from selenium.webdriver.support import expected_conditions as EC
import sys
import os
import random

# ========== 配置部分 ==========
CSV_FILE = '导出的条目.csv'  # 原始CSV文件名
XLSX_FILE = '导出的条目.xlsx'  # 转换后的Excel文件名
BATCHED_XLSX_FILE = '带批次_导出的条目.xlsx'  # 新增批次列后的Excel文件名
BATCH_SIZE = 50
CHROME_DRIVER_PATH = 'chromedriver.exe'  # chromedriver路径
WAIT_TIME = 15  # 页面元素最大等待秒数
# =============================

def csv_to_xlsx(csv_file, xlsx_file):
    # 自动检测编码和分隔符
    encodings = ['utf-8', 'gbk', 'gb2312', 'utf-16', 'latin-1']
    df = None
    
    for encoding in encodings:
        try:
            with open(csv_file, 'r', encoding=encoding) as f:
                first_line = f.readline()
                if ',' in first_line:
                    sep = ','
                elif ';' in first_line:
                    sep = ';'
                elif '\t' in first_line:
                    sep = '\t'
                else:
                    sep = ','
            
            df = pd.read_csv(csv_file, encoding=encoding, sep=sep)
            print(f'成功使用编码 {encoding} 读取文件')
            break
        except UnicodeDecodeError:
            continue
    
    if df is None:
        print('错误：无法使用常见编码格式读取CSV文件，请检查文件编码！')
        sys.exit(1)
    # 只保留Title和DOI两列
    keep_cols = []
    for col in df.columns:
        if col.strip().lower() == 'doi':
            keep_cols.append(col)
        if col.strip().lower() == 'title':
            keep_cols.append(col)
    if len(keep_cols) < 2:
        print('错误：原始文件中未找到DOI和Title两列，请检查文件内容！')
        sys.exit(1)
    df = df[keep_cols]
    # 对于DOI列为空的行，将Title列内容填入DOI列
    doi_col = [col for col in df.columns if col.strip().lower() == 'doi'][0]
    title_col = [col for col in df.columns if col.strip().lower() == 'title'][0]
    empty_doi_mask = df[doi_col].isna() | (df[doi_col].astype(str).str.strip() == '')
    df.loc[empty_doi_mask, doi_col] = df.loc[empty_doi_mask, title_col]
    df.to_excel(xlsx_file, index=False)
    print(f'已将{csv_file}仅保留DOI和Title两列（DOI为空时已用Title补全）并转换为Excel文件{xlsx_file}')
    return xlsx_file

def add_order_and_batchN(xlsx_file, batched_xlsx_file):
    df = pd.read_excel(xlsx_file)
    if 'Title' not in df.columns:
        df['Title'] = ''
    # 新增order列（在DOI前）
    doi_loc = df.columns.get_loc('DOI')
    if isinstance(doi_loc, int):
        # 使用pandas Series创建order列数据
        order_series = pd.Series(range(1, len(df) + 1))
        df.insert(doi_loc, 'order', order_series)
        # 新增batchN列（在DOI后）
        df.insert(doi_loc + 1, 'batchN', '')
    # 只对有DOI的行分配批次编号
    doi_mask = df['DOI'].notnull() & (df['DOI'].astype(str).str.strip() != '')
    doi_indices = df.index[doi_mask].tolist()
    for i, idx in enumerate(doi_indices):
        batch_num = (i // BATCH_SIZE) + 1
        df.at[idx, 'batchN'] = batch_num
    df.to_excel(batched_xlsx_file, index=False)
    # 分批统计
    batch_counts = df.loc[doi_mask, 'batchN'].value_counts().sort_index()
    total = doi_mask.sum()
    print(f'共计有DOI信息的文献：{total}篇')
    print(f'共分为{batch_counts.shape[0]}批')
    for batch, count in batch_counts.items():
        print(f'第{batch}批：{count}篇')
    return df, batch_counts.shape[0]

def process_batch_xlsx(batch_num):
    df = pd.read_excel(BATCHED_XLSX_FILE)
    if 'Title' not in df.columns:
        df['Title'] = ''
    batch_df = df[
        (df['batchN'].notnull()) &
        (df['DOI'].notnull()) & (df['DOI'].astype(str).str.strip() != '') &
        (df['batchN'].apply(lambda x: str(int(x)) if pd.notnull(x) and str(x).strip() != '' else '') == str(batch_num))
    ]
    total = len(batch_df)
    # 调试输出：以Excel表格格式输出当前批次所有行
    print(f'实际读取到的第{batch_num}批次所有行内容（Excel表格格式）：')
    # 输出列名
    print('\t'.join(batch_df.columns))
    # 输出每行内容
    for _, row in batch_df.iterrows():
        row_str = '\t'.join([str(x) if len(str(x)) <= 30 else str(x)[:15] + '...' for x in row])
        print(row_str)
    if total == 0:
        print(f'第{batch_num}批无文献可处理（已自动跳过DOI和batchN为空或NaN的行）。')
        return
    print(f'开始处理第{batch_num}批，共{total}篇文献。请确保已手动登录目标网站并将浏览器窗口置于前台。')
    driver = webdriver.Chrome(service=Service(CHROME_DRIVER_PATH))
    driver.get('https://spis.hnlat.com/scholar/list?val=10.1016%2Fj.ijhm.2025.104227&oaFirst=0')
    input("请手动登录目标网站并打开首页后，按回车键继续...")

    success, fail = 0, []
    start_time = time.time()
    for _, row in batch_df.iterrows():
        doi = str(row['DOI']).strip()
        title = str(row['Title']).strip()
        try:
            search_box = WebDriverWait(driver, WAIT_TIME).until(
                EC.presence_of_element_located((By.CSS_SELECTOR, "input[type='text']"))
            )
            search_box.clear()
            search_box.send_keys(doi)
            print(f"请手动点击'搜文章'按钮，并在结果页手动点击第1篇论文下的'文献求助'按钮……")
            checkbox = WebDriverWait(driver, 600).until(
                EC.element_to_be_clickable((By.XPATH, "//input[@type='checkbox']"))
            )
            checkbox.click()
            print(f"已自动勾选服务条款，请手动点击'确定'按钮。点击后将自动进入下一个DOI。")
            WebDriverWait(driver, 600).until_not(
                EC.presence_of_element_located((By.XPATH, "//input[@type='checkbox']"))
            )
            success += 1
            print(f"已处理 {success}/{total} 篇，当前处理：{title}")
            time.sleep(random.uniform(1, 2))
        except Exception as e:
            fail.append((doi, title, str(e)))
            print(f"处理失败：{doi} - {title} - {str(e)}")
            continue

    elapsed = int(time.time() - start_time)
    print(f"\n第{batch_num}批次处理完成！")
    print(f"成功处理：{success} 篇")
    if fail:
        print("失败DOI及原因：")
        for doi, title, reason in fail:
            print(f"{doi} - {title} - {reason}")
    print(f"总耗时：{elapsed // 60}分{elapsed % 60}秒")
    driver.quit()

if __name__ == '__main__':
    # 先将CSV转换为Excel，只保留DOI和Title两列
    if not os.path.exists(XLSX_FILE):
        csv_to_xlsx(CSV_FILE, XLSX_FILE)
    add_order_and_batchN(XLSX_FILE, BATCHED_XLSX_FILE)
    print("\n请指定要处理的批次编号（如1、2、3...）：")
    batch_num = input("请输入批次编号：").strip()
    process_batch_xlsx(batch_num)
    print("\n如需继续处理下一批，请重新运行本脚本并输入对应批次编号。") 