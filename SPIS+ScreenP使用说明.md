# SPIS+ScreenP 整合工具使用说明

## 概述

SPIS+ScreenP 是一个整合了两个独立功能的学术研究工具：

1. **SPIS 文献求助自动化工具** - 自动化处理学术文献求助流程
2. **ScreenP AI文献筛选工具** - 使用AI模型进行文献筛选和评估

两个工具保持完全独立的功能，但整合在同一个程序中，方便使用。

## 运行方式

```bash
python "SPIS+ScreenP.py"
```

程序启动后会**同时显示两个独立的图形界面窗口**：
- 🔴 **SPIS文献求助工具窗口** - 红色主题，用于自动化文献求助
- 🔵 **ScreenP AI文献筛选工具窗口** - 蓝色主题，用于AI文献筛选

两个工具完全独立，可以同时使用，互不干扰。

## 工具1：SPIS 文献求助自动化工具（红色主题窗口）

### 功能说明
- **图形化界面操作**，更加直观便捷
- 自动处理CSV文件，转换为Excel格式
- 自动添加序号和批次编号
- 使用Selenium自动化浏览器操作
- 在SPIS网站上自动进行文献求助
- 实时显示处理进度和日志

### 使用步骤
1. 在SPIS工具窗口中点击"📂 选择CSV文件"
2. 选择包含DOI和Title列的CSV文件
3. 点击"🔄 处理文件并生成批次"
4. 选择要处理的批次
5. 点击"🚀 打开浏览器"
6. 在弹出的Chrome浏览器中手动登录SPIS网站
7. 返回工具窗口，点击"▶️ 开始处理批次"
8. 程序会自动处理，按提示手动点击某些按钮
9. 查看处理进度和日志

### 界面功能
- **文件处理面板**：选择和处理CSV文件
- **批次管理面板**：选择批次和查看详情
- **浏览器控制面板**：控制浏览器和处理流程
- **进度监控面板**：实时显示处理进度和日志

### 生成文件
程序会自动生成以下文件：
- `导出的条目.xlsx` - 转换后的Excel文件
- `带批次_导出的条目.xlsx` - 添加批次信息的文件

## 工具2：ScreenP AI文献筛选工具（蓝色主题窗口）

### 功能说明
- 支持多个AI平台（DeepSeek、ChatAnywhere等）
- 基于自定义提示词进行文献筛选
- 图形化用户界面，操作简便
- 支持批量处理和实时保存
- 可设置处理范围和批次数量
- 实时显示处理过程和AI回复

### 使用步骤
1. 在ScreenP工具窗口中配置AI模型参数
2. 输入或修改筛选标准提示词（已预设默认提示词）
3. 点击"📁 导入文献文件"，选择包含title和Abstract Note列的文献文件
4. 设置处理范围和批次数量
5. 选择"🧪 测试运行 (前5条)"或"🚀 正式运行"
6. 查看详细的处理过程和AI评估结果
7. 点击"💾 导出结果"保存筛选结果

### 支持的文件格式
- Excel文件 (.xlsx, .xls)
- CSV文件 (自动检测编码)

### 必需的列名
文件必须包含以下列（支持多种命名方式）：
- **标题列**: title, 标题, titles, paper title, article title
- **摘要列**: abstract note, abstract, 摘要, abstracts, note, abstract notes, summary

## 依赖库

程序需要以下Python库：
```
pandas
selenium
requests
PyQt6
openpyxl
```

## 注意事项

1. **程序启动**：
   - 运行程序后会同时弹出两个窗口
   - 红色主题窗口是SPIS工具，蓝色主题窗口是ScreenP工具
   - 两个窗口完全独立，可以同时使用

2. **SPIS工具（红色窗口）**：
   - 需要手动登录SPIS网站
   - 确保chromedriver.exe在程序目录下
   - 处理过程中需要手动点击某些按钮
   - 图形界面操作更加直观便捷

3. **ScreenP工具（蓝色窗口）**：
   - 需要有效的AI API密钥
   - 网络连接稳定
   - 建议先进行测试运行
   - 已预设默认提示词，可直接使用

4. **通用**：
   - 两个工具功能完全独立，互不干扰
   - 可以同时在两个窗口中操作
   - 程序会自动处理文件编码问题
   - 关闭任一窗口不影响另一个工具的使用

## 文件结构

```
SPIS+ScreenP/
├── SPIS+ScreenP.py          # 主程序文件
├── SPIS+ScreenP使用说明.md   # 本说明文件
├── chromedriver.exe         # Chrome驱动（SPIS工具需要）
├── 导出的条目.csv           # SPIS工具输入文件
└── 结果文件/                # 生成的结果文件
```

## 故障排除

### 常见问题
1. **编码错误**：程序会自动尝试多种编码格式
2. **列名不匹配**：检查文件是否包含必需的列
3. **API调用失败**：检查网络连接和API密钥
4. **浏览器问题**：确保Chrome和chromedriver版本匹配

### 联系支持
如遇到问题，请检查：
1. 文件格式是否正确
2. 依赖库是否已安装
3. 网络连接是否正常
4. API密钥是否有效

---

**版本**: 1.0  
**更新日期**: 2025年1月  
**兼容性**: Python 3.7+, Windows 10+
