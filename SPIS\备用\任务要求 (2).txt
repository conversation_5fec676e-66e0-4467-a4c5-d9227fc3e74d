原始任务要求
# DOI自动化处理与文献下载程序

我需要一个自动化程序，能够从我提供的CSV文件（导出的条目.csv）中读取DOI号并执行以下操作：

## 第一阶段：文献求助处理
1. 从CSV文件中读取"DOI"列的内容，每行对应一篇论文的DOI号（如"10.1177/00472875231214743"）
2. 允许我选择使用哪个浏览器来执行自动化任务
3. 将这些DOI号一个接一个地输入到我已手动打开并登录的网站的搜索框中（如"搜索框及按键图"所示）
4. 每输入一个DOI号后，自动点击输入框右侧的蓝色"搜文章"按钮
5. 当搜索结果显示在当前页面后，找到文献条目下方的"文献求助"按钮（与"导出题录"、"收藏"等按钮并列，如"搜索结果图"所示）并点击
6. 当弹出"文献求助"窗口时（如"自动弹出窗口"图所示），自动勾选"请勾选《文献求助服务条款》"前面的复选框
7. 点击"确定"按钮完成当前DOI的处理
8. **每完成一个DOI的处理，显示实时进度信息（如"已处理 X/Y 篇，当前处理：[论文标题]"）**
9. 重复上述步骤，严格按照CSV文件中的顺序处理，如果DOI列中的条目少于50条，则处理所有条目；如果超过50条，则最多只处理前50条
10. **第一阶段完成后，显示进度报告，包括：成功处理的DOI数量、失败的DOI号列表（如有）及原因、总耗时等信息**

## 第二阶段：文献下载
1. 完成第一阶段后，在指定浏览器中打开指定的查找结果页面
2. 在该页面上，识别所有如"结果下载"图所示的下载结果项，每项包含论文标题、时间戳（如"2025-06-13 10:17:28"）和状态信息（如"待审核"）
3. **显示找到的符合时间条件的文献总数**
4. 对于所有时间戳在我指定日期及之前的文献，自动点击其"下载全文"按钮，文件将自动静默下载到浏览器默认下载文件夹中，无需额外干预
5. **每触发一次下载，显示实时进度信息（如"正在下载 X/Y 篇：[论文标题]"）**
6. **第二阶段完成后，显示任务总结信息，包括：成功触发下载的文献数量、符合时间条件的文献总数、总耗时等信息**

整个过程中，我只需要手动打开网站并登录，以及指定日期和浏览器，其余操作都由程序自动完成。程序应该能够处理导出的条目.csv文件中的DOI号，并按照上述步骤进行自动化处理，同时在每个步骤和阶段完成后提供详细的实时进度信息、进度报告和任务总结。


****修改后的正式任务要求
# 文献DOI自动化处理——详细执行过程文件（带批次标记与分批统计版）

## 一、准备工作

1. **准备CSV文件**  
   - 确保`导出的条目.csv`文件已导出并放在指定目录。
   - 文件中需包含“DOI”和“Title”两列。

2. **手动操作**  
   - 手动打开目标文献网站，并完成登录。
   - 保持浏览器窗口处于前台，后续自动化操作将在此窗口进行。

---

## 二、批次标记与分批统计

### 1. 读取与筛选

- 读取`导出的条目.csv`文件，**忽略DOI列内容为空的行**。
- 按顺序提取所有有效DOI及对应论文标题。

### 2. 自动添加“批次”列

- 在原有CSV文件基础上，**新增一列“批次”**，用于标记每条文献的处理批次。
- 每批最多50条文献，按顺序编号：
  - 第1批：前50条（如不足50条则为全部）
  - 第2批：第51-100条
  - 以此类推
- “批次”列内容为数字（如1、2、3…），表示该文献属于第几批。

### 3. 生成分批统计报告

- 自动统计并报告如下信息：
  - 有DOI信息的文献总数
  - 共分为几批
  - 每一批的文献数量（如：第1批50篇，第2批38篇）

---

## 三、指定批次处理

### 1. 由你指定批次

- 你可指定需要处理的批次（如“请处理第2批次的文献”），程序将**仅处理“批次”列为该数字的所有文献**。

### 2. 依次处理指定批次的每条DOI

对指定批次中的每一条DOI，执行以下步骤：

#### 步骤1：输入DOI并搜索

- 在网站首页的搜索框（如截图1所示）输入当前DOI号。
- 点击右侧蓝色“搜文章”按钮。

#### 步骤2：定位第一篇文献并点击“文献求助”

- 等待页面加载，**在搜索结果页面中，直接定位第1篇文献（序号为1）下方的“文献求助”按钮并点击**（如截图2所示）。

#### 步骤3：弹窗操作

- 等待弹出“文献求助”窗口（如截图3所示）。
- **邮箱已自动填写，无需操作。**
- 勾选“请勾选《文献求助服务条款》”前的复选框。
- 点击“确定”按钮，完成本条DOI的求助。

#### 步骤4：进度信息显示

- 每处理完一条，**实时显示进度信息**：  
  `已处理 X/Y 篇，当前处理：[论文标题]`

#### 步骤5：异常处理

- 若遇到页面加载失败、按钮未找到等异常，记录当前DOI、标题及失败原因，跳过继续下一个。

### 3. 当前批次结束后，生成进度报告

- 显示如下内容：
  - 成功处理的DOI数量
  - 失败的DOI号、标题及原因（如有）
  - 总耗时（自动统计）

---

## 四、注意事项

- 整个过程中，**只需手动登录和打开网站**，其余操作均可自动化完成。
- 进度信息和报告需在程序运行时和每批结束后自动显示，便于随时掌握任务进展和结果。
- 若需中断，可随时手动停止程序，已完成部分不受影响。

---

## 五、示例分批统计与进度报告格式

### 分批统计报告
```
共计有DOI信息的文献：88篇
共分为2批
第1批：50篇
第2批：38篇
```

### 实时进度信息
```
已处理 3/50 篇，当前处理：Tourist Rural Destination Restorative Capacity: Scale Development and Validation
```
### 批次报告
```
第2批次处理完成！
成功处理：36 篇
失败DOI及原因：
10.1177/00472875231213992 - 页面加载超时
10.1145/3139352 - 未找到“文献求助”按钮
总耗时：2分15秒
```

---

如需进一步细化某一步骤或需要自动化脚本模板，请随时告知！

```plaintext
共计有DOI信息的文献：88篇
共分为2批
第1批：50篇
第2批：38篇
```

```plaintext
已处理 3/50 篇，当前处理：Tourist Rural Destination Restorative Capacity: Scale Development and Validation
```

```plaintext
第2批次处理完成！
成功处理：36 篇
失败DOI及原因：
10.1177/00472875231213992 - 页面加载超时
10.1145/3139352 - 未找到“文献求助”按钮
总耗时：2分15秒
```