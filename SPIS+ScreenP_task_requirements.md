# SPIS+ScreenP 任务需求规范文档 v1.1

## 📋 项目概述

### 项目名称
SPIS+ScreenP 整合工具 v1.1

### 项目目标
开发一个集成两个独立学术研究工具的桌面应用程序，为学术研究人员提供文献求助自动化和AI文献筛选功能，提高学术研究效率。v1.1版本重点加强了数据管理和用户交互体验。

### 核心价值
- **效率提升**：自动化重复性文献处理任务
- **智能筛选**：基于AI的文献相关性评估
- **用户友好**：图形化界面，操作简便
- **功能独立**：两个工具可独立使用，互不干扰
- **✨ 数据管理**：可编辑的批次数据管理功能
- **✨ 智能切换**：自动化的浏览器和批次管理
- **✨ 多窗口操作**：非模态窗口设计，提升工作效率

## 🎯 任务需求分析

### 需求1：SPIS文献求助自动化工具

#### 业务需求
- **目标用户**：学术研究人员、图书馆员、文献管理员
- **使用场景**：批量处理学术文献DOI，在SPIS网站进行文献求助
- **痛点解决**：
  - 手动逐条输入DOI效率低下
  - 重复性操作容易出错
  - 批量处理缺乏系统化管理

#### 功能需求
1. **文件处理功能**
   - 支持CSV文件导入
   - 自动检测文件编码（UTF-8, GBK, GB2312等）
   - 自动识别分隔符（逗号、分号、制表符）
   - 数据清洗：保留DOI和Title列
   - 空值处理：Title填充空DOI字段

2. **批次管理功能**
   - 自动分批：每批50条记录
   - 序号生成：为每条记录添加order列
   - 批次标记：为每条记录添加batchN列
   - 批次统计：显示各批次记录数量

3. **✨ 智能批次切换功能** (v1.1新增)
   - 批次选择变化自动检测
   - 自动关闭当前浏览器窗口
   - 智能按钮状态管理
   - 用户友好的切换提示
   - 防止数据冲突和会话混乱

4. **✨ 可编辑批次详情功能** (v1.1新增)
   - Excel式数据编辑界面
   - 实时数据增删改查
   - 非模态窗口设计
   - 多窗口同时操作
   - 数据保存和恢复机制

5. **浏览器自动化功能**
   - Chrome浏览器控制
   - SPIS网站自动导航
   - DOI自动填写
   - 服务条款自动勾选
   - 半自动化流程控制
   - ✨ 批次切换时自动重启浏览器

6. **进度监控功能**
   - 实时处理进度显示
   - 详细操作日志记录
   - 错误信息捕获和显示
   - 处理时间统计
   - ✨ 多窗口状态同步

#### 技术需求
- **前端技术**：PyQt6图形界面框架
- **数据处理**：pandas库进行数据操作
- **浏览器控制**：Selenium WebDriver
- **文件格式**：支持CSV输入，Excel输出
- **✨ 窗口管理**：非模态窗口设计和多窗口管理
- **✨ 数据编辑**：可编辑表格组件和实时数据同步
- **✨ 状态管理**：智能状态检测和自动化流程控制

### 需求2：ScreenP AI文献筛选工具

#### 业务需求
- **目标用户**：学术研究人员、文献综述作者
- **使用场景**：基于特定标准筛选大量学术文献
- **痛点解决**：
  - 人工筛选文献耗时巨大
  - 筛选标准难以统一
  - 大规模文献处理能力不足

#### 功能需求
1. **AI模型配置功能**
   - 多平台支持：DeepSeek、ChatAnywhere等
   - 模型选择：支持多种AI模型
   - 参数配置：Max Tokens、Temperature等
   - API密钥管理

2. **提示词管理功能**
   - 预设专业提示词模板
   - 自定义提示词编辑
   - 针对特定研究领域优化
   - 筛选标准可视化配置

3. **数据处理功能**
   - 文件格式支持：Excel、CSV
   - 智能列名识别：title、Abstract Note等
   - 数据清洗和验证
   - 编码兼容性处理

4. **批量筛选功能**
   - 范围设置：指定起始和结束行
   - 批次处理：可配置批次数量
   - 测试模式：前5条记录验证
   - 实时保存：每条记录立即保存

5. **结果管理功能**
   - 多格式导出：Excel、CSV
   - 自动文件命名：包含时间戳
   - 结果可视化显示
   - 处理过程详细记录

#### 技术需求
- **前端技术**：PyQt6图形界面框架
- **AI集成**：RESTful API调用
- **数据处理**：pandas库
- **异步处理**：QThread多线程
- **网络通信**：requests库

## 🏗️ 技术规范

### 架构设计

#### 整体架构 (v1.1更新)
```
SPIS+ScreenP Application v1.1
├── Main Entry Point (main())
├── SPIS Tool Module (Enhanced)
│   ├── SPISLiteratureRequestTool (QMainWindow)
│   │   ├── Intelligent Batch Management ✨
│   │   ├── Auto Browser Switching ✨
│   │   └── Multi-window State Management ✨
│   ├── EditableBatchDetailWindow (QMainWindow) ✨ NEW
│   │   ├── Excel-style Data Editing ✨
│   │   ├── Real-time Data Sync ✨
│   │   └── Non-modal Window Design ✨
│   ├── File Processing Functions
│   ├── Browser Control Functions
│   └── Enhanced UI Components ✨
└── ScreenP Tool Module
    ├── AILiteratureScreeningTool (QMainWindow)
    ├── ProgressWorker (QObject)
    ├── AI API Integration
    └── UI Components
```

#### 设计模式
- **MVC模式**：Model-View-Controller分离
- **观察者模式**：Qt信号槽机制
- **工厂模式**：AI平台配置管理
- **单例模式**：应用程序实例管理

### 代码规范

#### Python代码标准
- **PEP 8**：遵循Python官方代码风格指南
- **类型注解**：使用typing模块进行类型标注
- **文档字符串**：所有函数和类包含详细docstring
- **异常处理**：完善的try-catch错误处理机制

#### 命名规范
```python
# 类名：大驼峰命名法
class SPISLiteratureRequestTool

# 函数名：小写下划线命名法
def spis_csv_to_xlsx()

# 常量：大写下划线命名法
SPIS_BATCH_SIZE = 50

# 变量名：小写下划线命名法
batch_count = 5
```

#### 代码组织
- **模块化设计**：功能相关代码组织在同一模块
- **配置分离**：配置参数集中管理
- **接口抽象**：定义清晰的函数接口
- **依赖注入**：减少模块间耦合

### 性能规范

#### 响应时间要求
- **界面响应**：用户操作响应时间 < 200ms
- **文件加载**：1000条记录文件加载 < 3秒
- **API调用**：单次AI API调用超时 60秒
- **批量处理**：1000条记录处理时间 < 30分钟

#### 资源使用限制
- **内存使用**：峰值内存使用 < 1GB
- **CPU使用**：平均CPU使用率 < 50%
- **磁盘空间**：临时文件总大小 < 100MB
- **网络带宽**：API调用频率 < 10次/分钟

### 安全规范

#### 数据安全
- **API密钥保护**：敏感信息不明文存储
- **数据传输**：HTTPS加密传输
- **本地数据**：临时文件及时清理
- **用户隐私**：不收集用户个人信息

#### 错误处理
- **异常捕获**：所有可能异常都有处理机制
- **错误日志**：详细记录错误信息和堆栈
- **用户提示**：友好的错误信息显示
- **恢复机制**：支持从错误状态恢复

## 📊 输入输出要求

### SPIS工具输入输出

#### 输入要求
1. **CSV文件格式**
   ```csv
   DOI,Title
   10.1016/j.example.2023.001,Example Paper Title 1
   10.1016/j.example.2023.002,Example Paper Title 2
   ```

2. **数据质量要求**
   - 必须包含DOI和Title列（列名不区分大小写）
   - DOI格式：标准DOI格式或文献标题
   - 编码格式：UTF-8、GBK、GB2312等常见编码
   - 文件大小：建议 < 10MB

#### 输出要求
1. **Excel文件格式**
   ```
   order | DOI | batchN | Title
   1     | 10.1016/... | 1 | Example Title 1
   2     | 10.1016/... | 1 | Example Title 2
   ```

2. **文件命名规范**
   - `导出的条目.xlsx`：基础转换文件
   - `带批次_导出的条目.xlsx`：添加批次信息的文件

### ScreenP工具输入输出

#### 输入要求
1. **文献数据文件格式**
   ```csv
   title,Abstract Note
   "AI Decision Making","This paper explores..."
   "Responsibility Attribution","This study examines..."
   ```

2. **数据质量要求**
   - 必须包含title和Abstract Note列
   - 标题和摘要不能为空
   - 支持Excel (.xlsx, .xls) 和CSV格式
   - 文件大小：建议 < 50MB

#### 输出要求
1. **筛选结果文件格式**
   ```
   序号 | title | Abstract Note | 符合度&判断理由
   1    | ...   | ...          | 符合度评估结果：85%...
   2    | ...   | ...          | 符合度评估结果：45%...
   ```

2. **文件命名规范**
   - `筛选结果_YYYYMMDD_HHMMSS.xlsx`
   - `测试结果_YYYYMMDD_HHMMSS.xlsx`

## ✅ 验收标准

### 功能验收标准

#### SPIS工具验收 (v1.1更新)
1. **文件处理验收**
   - [ ] 成功导入包含1000条记录的CSV文件
   - [ ] 正确识别DOI和Title列
   - [ ] 自动处理空值和数据清洗
   - [ ] 生成正确的批次信息

2. **✨ 智能批次管理验收** (v1.1新增)
   - [ ] 批次切换时自动关闭浏览器
   - [ ] 智能重置按钮状态
   - [ ] 显示友好的切换提示
   - [ ] 防止重复打开同一批次详情窗口

3. **✨ 可编辑批次详情验收** (v1.1新增)
   - [ ] 非模态窗口正常打开和关闭
   - [ ] Excel式表格编辑功能正常
   - [ ] 添加、删除行功能正常
   - [ ] 数据保存到Excel文件成功
   - [ ] 重置功能恢复原始数据
   - [ ] 多窗口同时操作无冲突

4. **浏览器控制验收**
   - [ ] 成功启动Chrome浏览器
   - [ ] 正确导航到SPIS网站
   - [ ] 自动填写DOI到搜索框
   - [ ] 自动勾选服务条款
   - [ ] ✨ 批次切换时自动重启浏览器

5. **进度监控验收**
   - [ ] 实时显示处理进度
   - [ ] 详细记录操作日志
   - [ ] 正确统计成功和失败数量
   - [ ] ✨ 多窗口状态同步正常

#### ScreenP工具验收
1. **AI配置验收**
   - [ ] 支持多个AI平台切换
   - [ ] 正确配置API参数
   - [ ] 成功调用AI API接口

2. **数据处理验收**
   - [ ] 成功导入Excel和CSV文件
   - [ ] 正确识别标题和摘要列
   - [ ] 支持设置处理范围

3. **筛选功能验收**
   - [ ] 测试模式正常运行
   - [ ] 批量处理功能正常
   - [ ] 实时保存处理结果
   - [ ] 正确导出筛选结果

### 性能验收标准

#### 响应性能
- [ ] 界面操作响应时间 < 200ms
- [ ] 1000条记录文件加载 < 5秒
- [ ] AI API单次调用 < 60秒
- [ ] 批量处理1000条记录 < 45分钟

#### 稳定性能
- [ ] 连续运行8小时无崩溃
- [ ] 处理10000条记录无内存泄漏
- [ ] 网络中断后能正常恢复
- [ ] 异常情况下数据不丢失

### 质量验收标准

#### 代码质量
- [ ] 代码覆盖率 > 80%
- [ ] 无严重安全漏洞
- [ ] 遵循PEP 8代码规范
- [ ] 完整的错误处理机制

#### 用户体验
- [ ] 界面美观，操作直观
- [ ] 错误提示信息友好
- [ ] 帮助文档完整清晰
- [ ] 支持常见操作系统

### 兼容性验收标准

#### 系统兼容性
- [ ] Windows 10/11 正常运行
- [ ] Python 3.7+ 版本兼容
- [ ] Chrome 浏览器各版本兼容
- [ ] 不同屏幕分辨率适配

#### 数据兼容性
- [ ] 支持多种文件编码格式
- [ ] 兼容不同Excel版本
- [ ] 处理各种CSV分隔符
- [ ] 支持大文件处理

## 📈 项目里程碑

### 阶段1：基础功能开发 (已完成)
- [x] SPIS工具核心功能实现
- [x] ScreenP工具核心功能实现
- [x] 基础图形界面开发
- [x] 双工具集成架构

### 阶段2：功能完善 (已完成)
- [x] 错误处理机制完善
- [x] 用户界面优化
- [x] 性能优化调整
- [x] 文档编写完成

### ✨ 阶段3：v1.1重大功能更新 (已完成)
- [x] 智能批次切换功能开发
- [x] 可编辑批次详情窗口开发
- [x] 非模态窗口设计实现
- [x] Excel式数据编辑功能
- [x] 按钮响应性优化
- [x] 多窗口管理机制
- [x] 自动浏览器管理功能

### 阶段4：测试验收 (进行中)
- [x] v1.1新功能测试验收
- [ ] 性能测试验收
- [ ] 用户体验测试
- [ ] 兼容性测试

### 阶段5：部署发布 (待开始)
- [ ] 打包发布版本
- [ ] 用户手册完善
- [ ] 技术支持准备
- [ ] 正式发布上线

## 🔄 维护和更新

### 维护计划
- **日常维护**：监控系统运行状态，及时处理用户反馈
- **定期更新**：每季度更新AI模型配置，优化处理算法
- **安全更新**：及时修复安全漏洞，更新依赖库版本
- **功能扩展**：根据用户需求添加新功能模块

### 技术债务管理
- **代码重构**：定期重构老旧代码，提高代码质量
- **性能优化**：持续优化系统性能，提升用户体验
- **文档更新**：保持文档与代码同步更新
- **测试覆盖**：逐步提高测试覆盖率，确保系统稳定性

---

**文档版本**: 1.1
**创建日期**: 2025年1月
**最后更新**: 2025年1月
**负责人**: 开发团队
**主要更新**: v1.1功能需求和技术规范更新
