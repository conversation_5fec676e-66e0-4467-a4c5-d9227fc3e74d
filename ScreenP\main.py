#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
AI文献筛选自动化工具 - 主程序
"""

import sys
import os
import json
import re
import subprocess
import platform
from typing import Dict, List, Optional, Tuple
from pathlib import Path
import time
import threading
import traceback

import pandas as pd
import requests
from PyQt6.QtWidgets import *
from PyQt6.QtCore import *
from PyQt6.QtGui import *

# AI平台配置
AI_PLATFORMS = {
    "deepseek": {
        "name": "DeepSeek",
        "api_key": "***********************************",
        "base_url": "https://api.deepseek.com/v1/chat/completions",
        "models": ["deepseek-chat", "deepseek-reasoner"],
        "default_model": "deepseek-chat"
    },
    "chatanywhere": {
        "name": "ChatAnywhere", 
        "api_key": "sk-rHjR1JnETK1p6brlIU3kjbmGL6U477fk8ObNtf4NEEKKDd7a",
        "base_url": "https://api.chatanywhere.tech/v1/chat/completions",
        "models": [
            "o3", "o3-2025-04-16", "o4-mini", "o4-mini-2025-04-16", "gpt-4.1",
            "gpt-4.1-2025-04-14", "gpt-4.1-mini", "gpt-4.1-mini-2025-04-14",
            "gpt-4.1-nano", "gpt-4.1-nano-2025-04-14", "gpt-3.5-turbo",
            "gpt-3.5-turbo-1106", "gpt-3.5-turbo-0125", "gpt-3.5-turbo-16k",
            "gpt-3.5-turbo-instruct", "gpt-4.5-preview", "gpt-4.5-preview-2025-02-27",
            "o1-mini", "o1-preview", "o3-mini", "o1", "gpt-4o-search-preview",
            "gpt-4o-search-preview-2025-03-11", "gpt-4o-mini-search-preview",
            "gpt-4o-mini-search-preview-2025-03-11", "gpt-4", "gpt-4o",
            "gpt-4o-2024-05-13", "gpt-4o-2024-08-06", "gpt-4o-2024-11-20",
            "chatgpt-4o-latest", "gpt-4o-mini", "gpt-4-0613", "gpt-4-turbo-preview",
            "gpt-4-0125-preview", "gpt-4-1106-preview", "gpt-4-vision-preview",
            "gpt-4-turbo", "gpt-4-turbo-2024-04-09", "gpt-3.5-turbo-ca",
            "gpt-4-ca", "gpt-4-turbo-ca", "gpt-4o-ca", "gpt-4o-mini-ca",
            "chatgpt-4o-latest-ca", "o1-mini-ca", "o1-preview-ca", "deepseek-reasoner",
            "deepseek-r1", "deepseek-v3", "claude-3-7-sonnet-20250219",
            "claude-3-5-sonnet-20240620", "claude-3-5-sonnet-20241022",
            "claude-3-5-haiku-20241022", "gemini-1.5-flash-latest",
            "gemini-1.5-pro-latest", "gemini-exp-1206", "gemini-2.0-flash-exp",
            "gemini-2.0-pro-exp-02-05", "gemini-2.0-flash", "gemini-2.5-pro-exp-03-25",
            "gemini-2.5-pro-preview-05-06", "gemini-2.5-flash-preview-04-17",
            "grok-3", "grok-3-reasoner", "grok-3-deepsearch"
        ],
        "default_model": "gemini-2.5-flash-preview-04-17"
    }
}

# 默认提示词
DEFAULT_PROMPT = """### **【正式版】AI论文筛选任务提示词**

#### **1. 角色与任务 (Role & Task)**
**# 角色定义：一位顶尖的跨学科研究学者**
你将扮演一名在学术界享有盛誉的资深学者，你的专业知识背景具有鲜明的跨学科特征，具体层次如下：
* **宏观学科领域 (Broad Academic Fields):**
    * 你的知识根基建立在【计算机科学（人工智能）、管理学、商学、法学 (Law)、科技伦理学 (Ethics of Technology)和公共管理 (Public Administration) 】这几大领域的交叉地带。这为你提供了分析问题的法律原则、伦理框架和治理视角。
* **核心研究领域 (Specific Research Area):**
    * 在上述宏观领域的基础上，你将研究焦点收窄，专攻于 **人工智能伦理与治理 (AI Ethics and Governance)**。这是你学术身份的核心，你致力于理解和解决新兴技术对社会规范、个人权利和公共秩序带来的挑战。
* **微观研究方向 (Micro-scopic Research Focus):**
    * 在你的核心研究领域内，你已经成为一个更细分、更前沿的微观方向上的权威专家。你目前的研究兴趣和工作全部围绕着 **自动化决策系统中的责任归因与法律适用性 (Responsibility Attribution and Legal Applicability in Automated Decision-Making Systems)**。你对这个微观方向的最新动态、关键争议（如"责任真空"问题）、理论前沿（如分布式责任理论）和重要文献（如欧盟《人工智能法案》的相关条款）了如指掌。

**任务指令：**
请你完全沉浸在这个专家角色中，运用你（作为该角色）积累的全部知识、洞察力和判断标准，来审视和评估我提供给你的每一篇关于AI的论文。你的判断将尤其关注一篇文献是否能够精准切入"AI决策"与"责任归因"的连接点。
你将扮演一位专业的科研助理，拥有信息科学、伦理学和法学的交叉学科背景。你的核心任务是协助我完成一项关于"人工智能决策与责任归因"的文献综述项目。我将会一篇一篇地提交论文的标题和摘要，你需要根据我提供的资料，严格按照既定的内容标准进行筛选和评估。

#### **2. 核心筛选标准 (Core Screening Criteria)**

你需要判断每篇论文是否**直接且深入地**探讨了以下两大主题的**交叉领域**：

* **主题A：AI决策或AI辅助决策**
    * 这不仅包括明确提及"AI决策"（AI decision-making）或"AI辅助决策"（AI-assisted decision-making）的文献。
    * **也应涵盖**：算法决策（algorithmic decision-making）、自动化决策系统（automated decision systems）、决策支持系统（decision support systems）、专家系统（expert systems）、推荐系统（recommender systems）、自主系统/武器（autonomous systems/weapons）、以及在特定领域（如医疗、金融、司法、军事）中由AI/算法驱动的判断与选择过程。

* **主题B：责任或责任归因**
    * 这不仅包括明确提及"责任"（responsibility）或"责任归因"（responsibility attribution）。
    * **也应涵盖**：问责/可责性（accountability）、法律责任（liability）、道德责任（moral responsibility）、过失/罪责（culpability/blameworthiness）、"责任差距"（responsibility gap）问题、以及与责任判定紧密相关的概念，如：可解释性（explainability/XAI）、透明度（transparency）、可审计性（auditability）、公平性（fairness）和偏见（bias）等伦理及法律议题。

**关键判断点**：一篇论文必须**同时**探讨主题A和主题B，并且将两者的**关联**作为其核心议题或重要研究内容之一。如果一篇论文只讨论AI决策的技术实现（纯主题A），或只在没有AI背景下讨论责任理论（纯主题B），那么它就不符合标准。

#### **3. 任务指令 (Task Instructions)**

对于我提交的每一篇论文，请你执行以下操作：

1.  **逐步思考（Think Step-by-Step）**：首先，通读标题和摘要，识别出与**主题A**和**主题B**相关的所有概念。然后，分析论文的核心论点，判断这两个主题的**结合紧密程度**。
2.  **生成评估结果**：基于你的分析，严格按照下述"输出格式"和"评估准则"生成评估报告。

#### **4. 评估准则 (Evaluation Criteria)**

请参考以下准则来确定"符合度"的百分比：

* **80% - 100% (高度符合)**：论文的**中心主题**就是AI决策与责任的交叉研究。标题和摘要都明确反映了这一点。
* **50% - 79% (中度符合)**：论文的**一个主要部分或重要的次要论点**是关于AI决策与责任的。摘要中清晰地讨论了这两个主题的关联，但可能不是唯一的焦点。
* **20% - 49% (低度符合)**：论文的核心是关于AI技术或某个应用领域，但在文中**顺带提及或简要讨论**了相关的责任、伦理问题。这种关联不是其研究的重点。
* **0% - 19% (基本不符)**：论文**几乎没有**将AI决策与责任问题联系起来，可能只是孤立地包含其中某个主题的关键词，或者完全不相关。

#### **5. 输出格式 (Output Format)**

请严格按照此格式反馈你的评估结果（与此无关的内容一律不要输出，只需要你输出下面的内容）：

**符合度评估结果：** [请在此处填写一个0%到100%的百分比数值]%

**判断理由：**
1.  **主题A（AI决策）相关性分析**：[请在此处详细说明论文如何涉及AI决策。]
2.  **主题B（责任）相关性分析**：[请在此处详细说明论文如何涉及责任问题。]
3.  **综合评估与打分逻辑**：[请在此处综合以上两点，详细解释你为何给予这个具体的符合度分数，并阐述两个主题在文中的结合程度。]


---

**提示词使用说明：**
您可以将以上内容作为对AI的初始设定。设定完成后，您就可以直接发送第一篇论文的【标题】和【摘要】了。AI将根据此处的指令进行处理。
尽管有多篇论文的标题和摘要，但我会一篇一篇地提供给你，请你按要求一篇一篇地对论文的符合度进行评估并给出判断的理由。
以下为需要你判断的某一篇论文的标题和摘要信息：

"""

class ProgressWorker(QObject):
    """进度工作线程"""
    progress_updated = pyqtSignal(int, int, int, int, str, int)  # current, total, batch_current, batch_total, status, elapsed_seconds
    result_ready = pyqtSignal(dict)
    error_occurred = pyqtSignal(str)
    finished = pyqtSignal()
    batch_completed = pyqtSignal(int, dict, int)  # batch_number, batch_results, elapsed_seconds
    process_info_updated = pyqtSignal(str)  # 处理过程信息
    
    def __init__(self):
        super().__init__()
        self.data = None
        self.platform_config = None
        self.prompt = None
        self.batch_count = 1
        self.max_tokens = 4096
        self.temperature = 0.7
        self.should_stop = False
        self.is_test_mode = False
        self.auto_save_enabled = True
        self.output_path_base = None
        self.start_row_offset = 0  # 起始行偏移量，用于显示正确的全局行号
        self.start_time = None  # 开始处理时间
    
    def setup(self, data, platform_config, prompt, batch_count, max_tokens, temperature):
        self.data = data
        self.platform_config = platform_config
        self.prompt = prompt
        self.batch_count = batch_count
        self.max_tokens = max_tokens
        self.temperature = temperature
        self.should_stop = False
    
    def set_auto_save(self, enabled, output_path_base=None):
        """设置自动保存参数"""
        self.auto_save_enabled = enabled
        self.output_path_base = output_path_base
    
    def stop(self):
        self.should_stop = True
    
    def run(self):
        """执行AI筛选任务"""
        try:
            # 记录开始时间
            self.start_time = time.time()
            
            total_count = len(self.data)
            batch_size = max(1, total_count // self.batch_count)
            results = []
            
            # 在开始处理时创建初始文件（测试模式和正式模式都创建）
            if self.auto_save_enabled and self.output_path_base:
                self.create_initial_file()
            
            for batch_idx in range(self.batch_count):
                if self.should_stop:
                    break
                    
                start_idx = batch_idx * batch_size
                if batch_idx == self.batch_count - 1:
                    end_idx = total_count
                else:
                    end_idx = start_idx + batch_size
                
                batch_data = self.data[start_idx:end_idx]
                batch_results = []
                
                for idx, row in enumerate(batch_data):
                    if self.should_stop:
                        break
                    
                    global_idx = start_idx + idx
                    status = "🧪 测试处理中..." if self.is_test_mode else f"正在处理第{batch_idx + 1}批次..."
                    
                    # 计算已用时间
                    elapsed_seconds = int(time.time() - self.start_time) if self.start_time else 0
                    
                    self.progress_updated.emit(
                        global_idx + 1, total_count,
                        idx + 1, len(batch_data),
                        status, elapsed_seconds
                    )
                    
                    # 发送记录编号信息，考虑起始行偏移
                    actual_row_number = self.start_row_offset + global_idx + 1
                    record_header = f"📊 记录编号：第{actual_row_number}条(原第{global_idx + 1}条)/第{batch_idx + 1}批（此批{len(batch_data)}条）/共{self.batch_count}批/共{total_count}条\n"
                    self.process_info_updated.emit(record_header)
                    
                    # 调用AI API
                    result = self.call_ai_api(row)
                    batch_results.append(result)
                    results.append(result)
                    
                    # 实时更新文件（每处理一条记录就更新，测试模式和正式模式都更新）
                    if self.auto_save_enabled and self.output_path_base:
                        self.update_progress_file(results)
                    
                    # 短暂延时，避免API调用过快
                    time.sleep(0.5)
                
                # 批次完成后的处理
                if not self.should_stop and not self.is_test_mode:
                    elapsed_seconds = int(time.time() - self.start_time) if self.start_time else 0
                    self.batch_completed.emit(batch_idx + 1, {"results": batch_results}, elapsed_seconds)
            
            if not self.should_stop:
                self.result_ready.emit({"results": results, "data": self.data})
            
        except Exception as e:
            self.error_occurred.emit(f"处理过程中发生错误：{str(e)}")
        finally:
            self.finished.emit()
    
    def create_initial_file(self):
        """创建初始的结果文件"""
        try:
            # 创建初始的空DataFrame
            df = pd.DataFrame(columns=['序号', 'title', 'Abstract Note', '符合度&判断理由'])
            
            # 保存初始文件
            if self.output_path_base.lower().endswith('.csv'):
                df.to_csv(self.output_path_base, index=False, encoding='utf-8-sig')
            else:
                df.to_excel(self.output_path_base, index=False)
            
            print(f"✓ 已创建初始结果文件: {self.output_path_base}")
        except Exception as e:
            print(f"创建初始文件失败: {e}")
    
    def update_progress_file(self, all_results):
        """实时更新进度文件"""
        try:
            # 准备导出数据
            export_data = []
            for i, result in enumerate(all_results):
                # 直接使用AI的完整评估结果
                ai_evaluation = result.get('ai_evaluation', '无评估结果')
                
                # 计算实际行号（考虑起始行偏移）
                actual_row_number = self.start_row_offset + i + 1
                
                export_data.append({
                    '序号': actual_row_number,
                    'title': result['title'],
                    'Abstract Note': result['abstract'],
                    '符合度&判断理由': ai_evaluation
                })
            
            # 创建DataFrame
            df = pd.DataFrame(export_data)
            
            # 保存文件
            if self.output_path_base.lower().endswith('.csv'):
                df.to_csv(self.output_path_base, index=False, encoding='utf-8-sig')
            else:
                df.to_excel(self.output_path_base, index=False)
                
        except Exception as e:
            print(f"更新进度文件失败: {e}")
    
    def call_ai_api(self, row_data):
        """调用AI API进行评估"""
        title = row_data.get('title', '')
        abstract = row_data.get('Abstract Note', '')
        
        # 构建完整的提示
        full_prompt = self.prompt + f"\ntitle: {title}; abstract: {abstract}"
        
        # 发送详细的处理开始信息
        truncated_title = title[:80] + '...' if len(title) > 80 else title
        truncated_abstract = abstract[:150] + '...' if len(abstract) > 150 else abstract
        
        start_info = f"{'='*60}\n"
        start_info += f"📤 开始处理新记录\n"
        start_info += f"📋 论文标题：{truncated_title}\n"
        start_info += f"📄 论文摘要：{truncated_abstract}\n"
        start_info += f"🤖 使用模型：{self.platform_config.get('model', 'Unknown')}\n"
        start_info += f"🔧 最大令牌：{self.max_tokens}\n"
        start_info += f"🌡️ 温度参数：{self.temperature}\n"
        start_info += f"🔗 API端点：{self.platform_config.get('base_url', 'Unknown')}\n"
        start_info += f"⏰ 开始时间：{time.strftime('%H:%M:%S')}\n"
        start_info += f"📝 提示词长度：{len(full_prompt)} 字符\n"
        start_info += f"{'='*60}\n\n"
        
        self.process_info_updated.emit(start_info)
        
        # 重试机制参数
        max_retries = 3
        retry_delays = [2, 5, 10]  # 每次重试的延时（秒）
        
        for attempt in range(max_retries):
            try:
                # 发送API调用状态
                if attempt == 0:
                    api_call_info = f"🚀 发送API请求...\n"
                else:
                    api_call_info = f"🔄 第 {attempt + 1} 次重试...\n"
                api_call_info += f"   - 正在连接服务器\n"
                api_call_info += f"   - 请求超时设置：60秒\n"
                api_call_info += f"   - 等待AI响应...\n\n"
                
                self.process_info_updated.emit(api_call_info)
                
                headers = {
                    "Content-Type": "application/json",
                    "Authorization": f"Bearer {self.platform_config['api_key']}"
                }
                
                data = {
                    "model": self.platform_config['model'],
                    "messages": [
                        {"role": "user", "content": full_prompt}
                    ],
                    "max_tokens": self.max_tokens,
                    "temperature": self.temperature
                }
                
                # 记录请求开始时间
                request_start_time = time.time()
                
                response = requests.post(
                    self.platform_config['base_url'],
                    headers=headers,
                    json=data,
                    timeout=60
                )
                
                # 计算响应时间
                response_time = time.time() - request_start_time
                
                if response.status_code == 200:
                    result = response.json()
                    content = result['choices'][0]['message']['content']
                    
                    # 发送成功响应信息
                    success_info = f"✅ API调用成功！\n"
                    if attempt > 0:
                        success_info += f"🎯 第 {attempt + 1} 次尝试成功\n"
                    success_info += f"⏱️ 响应时间：{response_time:.2f} 秒\n"
                    success_info += f"📊 HTTP状态：{response.status_code}\n"
                    success_info += f"📏 响应长度：{len(content)} 字符\n"
                    success_info += f"💰 估算Token使用：约 {len(full_prompt)//4 + len(content)//4} tokens\n\n"
                    
                    self.process_info_updated.emit(success_info)
                    
                    # 显示AI原始回复
                    raw_response_info = f"🤖 AI原始回复内容：\n"
                    raw_response_info += f"{'='*40}\n"
                    raw_response_info += f"{content}\n"
                    raw_response_info += f"{'='*40}\n\n"
                    
                    self.process_info_updated.emit(raw_response_info)
                    
                    # 解析结果 - 直接使用AI的完整回复
                    _, ai_full_response = self.parse_ai_response(content)
                    
                    # 发送解析结果信息
                    parsing_info = f"🔍 结果处理：\n"
                    parsing_info += f"   - AI回复处理：✅ 成功\n"
                    parsing_info += f"   - 回复内容长度：{len(ai_full_response)} 字符\n"
                    parsing_info += f"   - 内容状态：完整保存\n\n"
                    
                    self.process_info_updated.emit(parsing_info)
                    
                    # 显示最终处理结果
                    final_result_info = f"📋 最终处理结果：\n"
                    final_result_info += f"   📝 AI完整评估：{ai_full_response[:300]}{'...' if len(ai_full_response) > 300 else ''}\n"
                    final_result_info += f"   ✅ 数据状态：已保存到结果集\n"
                    final_result_info += f"   ⏰ 完成时间：{time.strftime('%H:%M:%S')}\n"
                    final_result_info += f"{'='*60}\n\n"
                    
                    self.process_info_updated.emit(final_result_info)
                    
                    return {
                        'title': title,
                        'abstract': abstract,
                        'ai_evaluation': ai_full_response,
                        'raw_response': content
                    }
                else:
                    # HTTP错误，记录并可能重试
                    error_msg = f"HTTP {response.status_code}: {response.text[:200]}"
                    if attempt < max_retries - 1:
                        retry_info = f"⚠️ 请求失败，{retry_delays[attempt]}秒后重试...\n"
                        retry_info += f"   - 错误：{error_msg}\n"
                        retry_info += f"   - 剩余重试次数：{max_retries - attempt - 1}\n\n"
                        self.process_info_updated.emit(retry_info)
                        time.sleep(retry_delays[attempt])
                        continue
                    else:
                        # 最后一次尝试也失败了
                        error_detail = f"❌ API调用最终失败！\n"
                        error_detail += f"📊 HTTP状态码：{response.status_code}\n"
                        error_detail += f"🔍 错误详情：{error_msg}\n"
                        error_detail += f"🔄 已重试 {max_retries} 次\n"
                        error_detail += f"🔧 建议处理：检查API密钥、配额或网络连接\n"
                        error_detail += f"{'='*60}\n\n"
                        
                        self.process_info_updated.emit(error_detail)
                        
                        return {
                            'title': title,
                            'abstract': abstract,
                            'ai_evaluation': f'API调用失败 (重试{max_retries}次): HTTP {response.status_code} - {response.text[:100]}',
                            'raw_response': ''
                        }
                        
            except Exception as e:
                # 网络连接异常等，进行重试
                error_type = type(e).__name__
                error_msg = str(e)
                
                if attempt < max_retries - 1:
                    # 还有重试机会
                    retry_info = f"⚠️ 连接异常，{retry_delays[attempt]}秒后重试...\n"
                    retry_info += f"   - 异常类型：{error_type}\n"
                    retry_info += f"   - 异常信息：{error_msg[:200]}{'...' if len(error_msg) > 200 else ''}\n"
                    retry_info += f"   - 剩余重试次数：{max_retries - attempt - 1}\n\n"
                    self.process_info_updated.emit(retry_info)
                    time.sleep(retry_delays[attempt])
                    continue
                else:
                    # 最后一次尝试也失败了
                    exception_info = f"❌ 连接最终失败！\n"
                    exception_info += f"🚨 异常类型：{error_type}\n"
                    exception_info += f"📝 异常信息：{error_msg}\n"
                    exception_info += f"🔄 已重试 {max_retries} 次\n"
                    exception_info += f"📍 可能原因：\n"
                    exception_info += f"   - 网络连接不稳定\n"
                    exception_info += f"   - API服务器临时不可用\n"
                    exception_info += f"   - 防火墙或代理问题\n"
                    exception_info += f"🔧 建议处理：检查网络连接和API配置\n"
                    exception_info += f"{'='*60}\n\n"
                    
                    self.process_info_updated.emit(exception_info)
                    
                    return {
                        'title': title,
                        'abstract': abstract,
                        'ai_evaluation': f'连接异常 (重试{max_retries}次): {error_type} - {str(e)[:100]}',
                        'raw_response': ''
                    }
        
        # 理论上不会到达这里，但作为保险
        return {
            'title': title,
            'abstract': abstract,
            'ai_evaluation': '未知错误',
            'raw_response': ''
        }
    
    def parse_ai_response(self, content: str) -> Tuple[str, str]:
        """解析AI响应，直接返回完整的AI回复内容"""
        try:
            # 直接返回AI的完整回复内容，不需要单独提取符合度
            # AI的回复中已经包含了符合度评估结果和完整的判断理由
            return "AI完整评估", content.strip()
            
        except Exception as e:
            print(f"处理AI响应时发生错误: {e}")
            return "处理错误", f"处理异常: {str(e)}"

class AILiteratureScreeningTool(QMainWindow):
    """AI文献筛选工具主窗口"""
    
    def __init__(self):
        super().__init__()
        self.data = None
        self.worker = None
        self.thread = None
        self.results = None
        self.result_file_path = None  # 存储导入的结果文件路径
        self.init_ui()
        
    def init_ui(self):
        """初始化用户界面"""
        self.setWindowTitle("🔬 AI文献筛选自动化工具")
        self.setGeometry(100, 100, 1200, 1000)  # 增加高度从800到1000
        self.setStyleSheet(self.get_stylesheet())
        
        # 创建中央窗口
        central_widget = QWidget()
        self.setCentralWidget(central_widget)
        
        # 创建主布局
        main_layout = QVBoxLayout(central_widget)
        main_layout.setSpacing(20)
        main_layout.setContentsMargins(20, 20, 20, 20)
        
        # 创建滚动区域
        scroll_area = QScrollArea()
        scroll_area.setWidgetResizable(True)
        scroll_area.setHorizontalScrollBarPolicy(Qt.ScrollBarPolicy.ScrollBarAsNeeded)
        scroll_area.setVerticalScrollBarPolicy(Qt.ScrollBarPolicy.ScrollBarAsNeeded)
        scroll_area.setMinimumHeight(900)  # 设置最小高度确保内容可见
        
        scroll_widget = QWidget()
        scroll_widget.setMinimumHeight(900)  # 设置内容widget的最小高度
        scroll_layout = QVBoxLayout(scroll_widget)
        
        # 标题
        title_label = QLabel("🔬 AI文献筛选自动化工具")
        title_label.setObjectName("title")
        scroll_layout.addWidget(title_label)
        
        # AI模型配置面板
        self.create_ai_config_panel(scroll_layout)
        
        # 提示词配置面板
        self.create_prompt_panel(scroll_layout)
        
        # 数据导入和任务配置面板
        self.create_data_panel(scroll_layout)
        
        # 进度监控面板
        self.create_progress_panel(scroll_layout)
        
        scroll_area.setWidget(scroll_widget)
        main_layout.addWidget(scroll_area)
        
        # 确保窗口有足够的显示空间
        self.showMaximized()  # 启动时最大化窗口
        self.setMinimumSize(1200, 1000)  # 设置最小窗口尺寸
        
    def create_ai_config_panel(self, parent_layout):
        """创建AI模型配置面板"""
        group = QGroupBox("🤖 AI模型配置")
        group.setObjectName("config-group")
        layout = QVBoxLayout(group)
        
        # 平台选择
        platform_layout = QHBoxLayout()
        platform_layout.addWidget(QLabel("选择AI平台:"))
        
        self.platform_combo = QComboBox()
        for key, config in AI_PLATFORMS.items():
            self.platform_combo.addItem(config["name"], key)
        self.platform_combo.currentTextChanged.connect(self.on_platform_changed)
        platform_layout.addWidget(self.platform_combo)
        platform_layout.addStretch()
        
        layout.addLayout(platform_layout)
        
        # API Key配置
        api_layout = QHBoxLayout()
        api_layout.addWidget(QLabel("API Key:"))
        
        self.api_key_input = QLineEdit()
        self.api_key_input.setEchoMode(QLineEdit.EchoMode.Password)  # 隐藏输入内容
        self.api_key_input.setPlaceholderText("API密钥已预设，如需修改请输入新的密钥")
        api_layout.addWidget(self.api_key_input)
        
        # 显示/隐藏API Key按钮
        self.show_api_key_btn = QPushButton("👁")
        self.show_api_key_btn.setMaximumWidth(40)
        self.show_api_key_btn.setToolTip("显示/隐藏API Key")
        self.show_api_key_btn.clicked.connect(self.toggle_api_key_visibility)
        api_layout.addWidget(self.show_api_key_btn)
        
        layout.addLayout(api_layout)
        
        # 模型选择
        model_layout = QHBoxLayout()
        model_layout.addWidget(QLabel("选择模型:"))
        
        self.model_combo = QComboBox()
        self.model_combo.setMinimumWidth(400)  # 设置最小宽度
        self.model_combo.setSizeAdjustPolicy(QComboBox.SizeAdjustPolicy.AdjustToContents)  # 根据内容调整大小
        model_layout.addWidget(self.model_combo)
        model_layout.addStretch()
        
        layout.addLayout(model_layout)
        
        # 参数配置
        params_layout = QGridLayout()
        
        # Max Tokens
        params_layout.addWidget(QLabel("Max Tokens:"), 0, 0)
        self.max_tokens_slider = QSlider(Qt.Orientation.Horizontal)
        self.max_tokens_slider.setRange(0, 8192)
        self.max_tokens_slider.setValue(4096)
        self.max_tokens_slider.valueChanged.connect(self.update_max_tokens_input)
        params_layout.addWidget(self.max_tokens_slider, 0, 1)
        
        self.max_tokens_input = QSpinBox()
        self.max_tokens_input.setRange(0, 8192)
        self.max_tokens_input.setValue(4096)
        self.max_tokens_input.valueChanged.connect(self.update_max_tokens_slider)
        params_layout.addWidget(self.max_tokens_input, 0, 2)
        
        # Temperature
        params_layout.addWidget(QLabel("Temperature:"), 1, 0)
        self.temperature_slider = QSlider(Qt.Orientation.Horizontal)
        self.temperature_slider.setRange(0, 200)  # 0-2.00
        self.temperature_slider.setValue(70)      # 0.70
        self.temperature_slider.valueChanged.connect(self.update_temperature_input)
        params_layout.addWidget(self.temperature_slider, 1, 1)
        
        self.temperature_input = QDoubleSpinBox()
        self.temperature_input.setRange(0.0, 2.0)
        self.temperature_input.setSingleStep(0.01)
        self.temperature_input.setDecimals(2)  # 设置小数位数
        self.temperature_input.setValue(0.7)
        self.temperature_input.valueChanged.connect(self.update_temperature_slider)
        params_layout.addWidget(self.temperature_input, 1, 2)
        
        layout.addLayout(params_layout)
        
        parent_layout.addWidget(group)
        
        # 初始化默认平台
        self.on_platform_changed()
        
    def toggle_api_key_visibility(self):
        """切换API Key显示/隐藏"""
        if self.api_key_input.echoMode() == QLineEdit.EchoMode.Password:
            self.api_key_input.setEchoMode(QLineEdit.EchoMode.Normal)
            self.show_api_key_btn.setText("🙈")
        else:
            self.api_key_input.setEchoMode(QLineEdit.EchoMode.Password)
            self.show_api_key_btn.setText("👁")
        
    def on_platform_changed(self):
        """平台选择改变时的处理"""
        platform_key = self.platform_combo.currentData()
        if platform_key:
            config = AI_PLATFORMS[platform_key]
            self.model_combo.clear()
            self.model_combo.addItems(config["models"])
            
            # 设置默认模型
            default_model = config["default_model"]
            index = self.model_combo.findText(default_model)
            if index >= 0:
                self.model_combo.setCurrentIndex(index)
            
            # 设置默认API Key
            self.api_key_input.setText(config["api_key"])
            self.api_key_input.setPlaceholderText(f"默认API Key已设置 ({config['name']})")
                
    def update_max_tokens_input(self, value):
        """更新Max Tokens输入框"""
        self.max_tokens_input.setValue(value)
        
    def update_max_tokens_slider(self, value):
        """更新Max Tokens滑块"""
        self.max_tokens_slider.setValue(value)
        
    def update_temperature_input(self, value):
        """更新Temperature输入框"""
        self.temperature_input.setValue(value / 100.0)
        
    def update_temperature_slider(self, value):
        """更新Temperature滑块"""
        self.temperature_slider.setValue(int(value * 100))
        
    def create_prompt_panel(self, parent_layout):
        """创建提示词配置面板"""
        group = QGroupBox("📝 筛选标准提示词")
        group.setObjectName("config-group")
        layout = QVBoxLayout(group)
        
        self.prompt_text = QTextEdit()
        self.prompt_text.setPlaceholderText("请在此处粘贴您的筛选标准提示词...")
        self.prompt_text.setMinimumHeight(300)
        layout.addWidget(self.prompt_text)
        
        parent_layout.addWidget(group)
        
    def create_data_panel(self, parent_layout):
        """创建数据导入和任务配置面板"""
        group = QGroupBox("📊 数据导入与任务配置")
        group.setObjectName("config-group")
        layout = QVBoxLayout(group)
        
        # 文件导入
        file_layout = QHBoxLayout()
        self.import_btn = QPushButton("📁 导入文献文件")
        self.import_btn.clicked.connect(self.import_file)
        file_layout.addWidget(self.import_btn)
        
        self.file_label = QLabel("未选择文件")
        self.file_label.setStyleSheet("color: #666; font-style: italic;")
        file_layout.addWidget(self.file_label)
        file_layout.addStretch()
        
        layout.addLayout(file_layout)
        
        # 处理范围配置
        range_group = QGroupBox("🎯 处理范围设置")
        range_group.setStyleSheet("QGroupBox { font-size: 14px; }")
        range_layout = QGridLayout(range_group)
        
        # 起始行
        range_layout.addWidget(QLabel("起始行:"), 0, 0)
        self.start_row_spin = QSpinBox()
        self.start_row_spin.setRange(1, 999999)
        self.start_row_spin.setValue(1)
        self.start_row_spin.valueChanged.connect(self.update_range_info)
        range_layout.addWidget(self.start_row_spin, 0, 1)
        
        # 结束行
        range_layout.addWidget(QLabel("结束行:"), 0, 2)
        self.end_row_spin = QSpinBox()
        self.end_row_spin.setRange(1, 999999)
        self.end_row_spin.setValue(1)
        self.end_row_spin.valueChanged.connect(self.update_range_info)
        range_layout.addWidget(self.end_row_spin, 0, 3)
        
        # 快速设置按钮
        self.all_rows_btn = QPushButton("📋 全部行")
        self.all_rows_btn.clicked.connect(self.set_all_rows)
        self.all_rows_btn.setEnabled(False)
        self.all_rows_btn.setToolTip("设置为处理全部数据")
        range_layout.addWidget(self.all_rows_btn, 0, 4)
        
        # 范围信息显示
        self.range_info_label = QLabel("请先导入文件")
        self.range_info_label.setStyleSheet("color: #666; font-style: italic;")
        range_layout.addWidget(self.range_info_label, 1, 0, 1, 5)
        
        layout.addWidget(range_group)
        
        # 批次配置
        batch_layout = QHBoxLayout()
        batch_layout.addWidget(QLabel("总批次数:"))
        
        self.batch_count_spin = QSpinBox()
        self.batch_count_spin.setRange(1, 50)
        self.batch_count_spin.setValue(5)
        self.batch_count_spin.valueChanged.connect(self.update_batch_info)
        batch_layout.addWidget(self.batch_count_spin)
        
        self.batch_info_label = QLabel("")
        batch_layout.addWidget(self.batch_info_label)
        batch_layout.addStretch()
        
        layout.addLayout(batch_layout)
        
        # 实时保存配置
        save_layout = QHBoxLayout()
        self.auto_save_checkbox = QCheckBox("启用实时保存")
        self.auto_save_checkbox.setChecked(True)
        self.auto_save_checkbox.setToolTip("每完成一个批次自动保存中间结果")
        save_layout.addWidget(self.auto_save_checkbox)
        save_layout.addStretch()
        
        layout.addLayout(save_layout)
        
        # 控制按钮
        control_layout = QHBoxLayout()
        
        # 测试运行按钮
        self.test_btn = QPushButton("🧪 测试运行 (前5条)")
        self.test_btn.clicked.connect(self.test_screening)
        self.test_btn.setEnabled(False)
        self.test_btn.setToolTip("处理前5条记录，验证程序配置是否正确")
        control_layout.addWidget(self.test_btn)
        
        # 正式运行按钮
        self.run_btn = QPushButton("🚀 正式运行")
        self.run_btn.clicked.connect(self.run_screening)
        self.run_btn.setEnabled(False)
        control_layout.addWidget(self.run_btn)
        
        # 中止按钮
        self.stop_btn = QPushButton("⏹ 中止")
        self.stop_btn.clicked.connect(self.stop_screening)
        self.stop_btn.setEnabled(False)
        control_layout.addWidget(self.stop_btn)
        
        control_layout.addStretch()
        
        layout.addLayout(control_layout)
        
        parent_layout.addWidget(group)
        
    def create_progress_panel(self, parent_layout):
        """创建进度监控面板"""
        group = QGroupBox("📈 进度监控")
        group.setObjectName("config-group")
        layout = QVBoxLayout(group)
        
        # 主进度条
        self.main_progress = QProgressBar()
        self.main_progress.setVisible(False)
        layout.addWidget(self.main_progress)
        
        # 状态标签
        self.status_label = QLabel("就绪")
        self.status_label.setAlignment(Qt.AlignmentFlag.AlignCenter)
        layout.addWidget(self.status_label)
        
        # 处理过程显示框
        process_label = QLabel("🔍 处理过程详情:")
        process_label.setStyleSheet("font-weight: bold; margin-top: 10px;")
        layout.addWidget(process_label)
        
        self.process_text = QTextEdit()
        self.process_text.setMaximumHeight(250)  # 减少高度从300到250
        self.process_text.setPlainText("等待开始处理...")
        self.process_text.setReadOnly(True)
        self.process_text.setStyleSheet("""
            QTextEdit {
                background-color: #f8f9fa;
                border: 1px solid #dee2e6;
                border-radius: 5px;
                padding: 8px;
                font-family: 'Consolas', 'Monaco', monospace;
                font-size: 10px;
                color: #495057;
            }
        """)
        layout.addWidget(self.process_text)
        
        # 说明文字
        info_label = QLabel("💡 提示：建议先进行测试运行，验证配置正确后再正式运行")
        info_label.setStyleSheet("color: #7f8c8d; font-style: italic; font-size: 12px;")
        info_label.setWordWrap(True)
        layout.addWidget(info_label)
        
        # 导出按钮
        self.export_btn = QPushButton("💾 导出结果")
        self.export_btn.clicked.connect(self.export_results)
        self.export_btn.setEnabled(False)
        layout.addWidget(self.export_btn)
        
        parent_layout.addWidget(group)
        
        # 添加结果文件后处理面板
        self.create_result_processing_panel(parent_layout)
    
    def create_result_processing_panel(self, parent_layout):
        """创建结果文件后处理面板"""
        group = QGroupBox("📈 结果文件后处理")
        group.setObjectName("config-group")
        layout = QVBoxLayout(group)
        
        # 说明文字
        info_label = QLabel("💡 从已完成的结果文件中提取符合度评估数值，生成新的分析文件")
        info_label.setStyleSheet("color: #7f8c8d; font-style: italic; font-size: 12px; margin-bottom: 10px;")
        info_label.setWordWrap(True)
        layout.addWidget(info_label)
        
        # 文件导入行
        file_layout = QHBoxLayout()
        self.result_import_btn = QPushButton("📁 导入结果文件")
        self.result_import_btn.clicked.connect(self.import_result_file)
        file_layout.addWidget(self.result_import_btn)
        
        self.result_file_label = QLabel("未选择结果文件")
        self.result_file_label.setStyleSheet("color: #666; font-style: italic;")
        file_layout.addWidget(self.result_file_label)
        file_layout.addStretch()
        
        layout.addLayout(file_layout)
        
        # 处理按钮
        process_layout = QHBoxLayout()
        self.process_result_btn = QPushButton("🔢 运行增加评估数值列")
        self.process_result_btn.clicked.connect(self.process_result_file)
        self.process_result_btn.setEnabled(False)
        self.process_result_btn.setToolTip("从'符合度&判断理由'列提取百分比数值，生成新列")
        process_layout.addWidget(self.process_result_btn)
        process_layout.addStretch()
        
        layout.addLayout(process_layout)
        
        parent_layout.addWidget(group)
        
    def get_stylesheet(self):
        """获取样式表"""
        return """
            QMainWindow {
                background-color: #f5f5f5;
            }
            QLabel#title {
                font-size: 24px;
                font-weight: bold;
                color: #2c3e50;
                padding: 20px;
                background: qlineargradient(x1:0, y1:0, x2:1, y2:0, 
                    stop:0 #3498db, stop:1 #2980b9);
                color: white;
                border-radius: 10px;
                margin-bottom: 10px;
            }
            QGroupBox {
                font-size: 16px;
                font-weight: bold;
                border: 2px solid #bdc3c7;
                border-radius: 10px;
                margin-top: 10px;
                padding-top: 10px;
                background-color: white;
            }
            QGroupBox#config-group {
                background: qlineargradient(x1:0, y1:0, x2:0, y2:1, 
                    stop:0 #ffffff, stop:1 #f8f9fa);
            }
            QGroupBox::title {
                subcontrol-origin: margin;
                left: 20px;
                padding: 0 10px 0 10px;
                color: #2c3e50;
            }
            QPushButton {
                background-color: #3498db;
                border: none;
                color: white;
                padding: 10px 20px;
                font-size: 14px;
                border-radius: 5px;
                font-weight: bold;
            }
            QPushButton:hover {
                background-color: #2980b9;
            }
            QPushButton:pressed {
                background-color: #21618c;
            }
            QPushButton:disabled {
                background-color: #bdc3c7;
                color: #7f8c8d;
            }
            QComboBox, QSpinBox, QDoubleSpinBox {
                padding: 8px;
                border: 2px solid #bdc3c7;
                border-radius: 5px;
                background-color: white;
                font-size: 14px;
                min-width: 200px;
            }
            QComboBox {
                min-width: 400px;
            }
            QComboBox QAbstractItemView {
                border: 2px solid #3498db;
                selection-background-color: #3498db;
                selection-color: white;
                background-color: white;
                min-width: 400px;
            }
            QComboBox:focus, QSpinBox:focus, QDoubleSpinBox:focus {
                border-color: #3498db;
            }
            QLineEdit {
                padding: 8px;
                border: 2px solid #bdc3c7;
                border-radius: 5px;
                background-color: white;
                font-size: 14px;
            }
            QLineEdit:focus {
                border-color: #3498db;
            }
            QTextEdit {
                border: 2px solid #bdc3c7;
                border-radius: 5px;
                padding: 10px;
                font-family: 'Consolas', 'Monaco', monospace;
                font-size: 12px;
                background-color: #f8f9fa;
            }
            QTextEdit:focus {
                border-color: #3498db;
            }
            QProgressBar {
                border: 2px solid #bdc3c7;
                border-radius: 5px;
                text-align: center;
                font-weight: bold;
                font-size: 14px;
            }
            QProgressBar::chunk {
                background-color: qlineargradient(x1:0, y1:0, x2:0, y2:1, 
                    stop:0 #3498db, stop:1 #2980b9);
                border-radius: 3px;
            }
            QSlider::groove:horizontal {
                border: 1px solid #bdc3c7;
                height: 8px;
                background: #ecf0f1;
                margin: 2px 0;
                border-radius: 4px;
            }
            QSlider::handle:horizontal {
                background: #3498db;
                border: 1px solid #2980b9;
                width: 18px;
                margin: -2px 0;
                border-radius: 9px;
            }
            QSlider::handle:horizontal:hover {
                background: #2980b9;
            }
        """
        
    def import_file(self):
        """导入文献文件"""
        file_path, _ = QFileDialog.getOpenFileName(
            self, "选择文献文件", "", 
            "Excel/CSV Files (*.xlsx *.xls *.csv)"
        )
        
        if file_path:
            try:
                # 根据文件扩展名选择读取方法
                if file_path.endswith('.csv'):
                    # 自动检测CSV文件编码
                    encodings = ['utf-8', 'gbk', 'gb2312', 'utf-8-sig', 'latin1', 'cp1252']
                    df = None
                    used_encoding = None
                    
                    for encoding in encodings:
                        try:
                            df = pd.read_csv(file_path, encoding=encoding)
                            used_encoding = encoding
                            print(f"✓ 成功使用 {encoding} 编码读取文件")
                            break
                        except UnicodeDecodeError:
                            print(f"✗ {encoding} 编码读取失败，尝试下一个...")
                            continue
                        except Exception as e:
                            print(f"✗ 使用 {encoding} 编码时发生其他错误: {e}")
                            continue
                    
                    if df is None:
                        QMessageBox.critical(
                            self, "编码错误", 
                            "无法识别文件编码格式。\n\n"
                            "请尝试：\n"
                            "1. 用Excel打开CSV文件，另存为UTF-8格式\n"
                            "2. 或将文件转换为Excel格式(.xlsx)"
                        )
                        return
                else:
                    df = pd.read_excel(file_path)
                    used_encoding = "Excel格式"
                
                # 显示原始数据信息
                original_rows = len(df)
                print(f"原始文件行数: {original_rows} (包括标题行)")
                print(f"数据行数: {original_rows - 1}")
                
                # 打印调试信息，显示实际的列名
                print("CSV文件中的列名:")
                for i, col in enumerate(df.columns):
                    print(f"  {i+1}. '{col}'")
                
                # 创建列名映射（忽略大小写和前后空格）
                column_mapping = {}
                available_columns = [col.strip().lower() for col in df.columns]
                original_columns = [col.strip() for col in df.columns]
                
                # 查找title列
                title_col = None
                for i, col in enumerate(available_columns):
                    if col in ['title', '标题', 'titles', 'paper title', 'article title']:
                        title_col = original_columns[i]
                        break
                
                # 查找Abstract Note列  
                abstract_col = None
                for i, col in enumerate(available_columns):
                    if col in ['abstract note', 'abstract', '摘要', 'abstracts', 'note', 'abstract notes', 'summary']:
                        abstract_col = original_columns[i]
                        break
                
                # 检查是否找到了必要的列
                if title_col is None:
                    available_cols = ', '.join([f"'{col}'" for col in df.columns])
                    QMessageBox.warning(
                        self, "未找到标题列", 
                        f"未能找到标题列。\n\n"
                        f"支持的标题列名: 'title', '标题', 'titles', 'paper title', 'article title'\n\n"
                        f"文件中的列名: {available_cols}\n\n"
                        f"请确保文件包含标题列，或重命名相应列。"
                    )
                    return
                
                if abstract_col is None:
                    available_cols = ', '.join([f"'{col}'" for col in df.columns])
                    QMessageBox.warning(
                        self, "未找到摘要列", 
                        f"未能找到摘要列。\n\n"
                        f"支持的摘要列名: 'abstract note', 'abstract', '摘要', 'abstracts', 'note', 'abstract notes', 'summary'\n\n"
                        f"文件中的列名: {available_cols}\n\n"
                        f"请确保文件包含摘要列，或重命名相应列。"
                    )
                    return
                
                # 重命名列名以统一格式
                df = df.rename(columns={title_col: 'title', abstract_col: 'Abstract Note'})
                
                # 详细的数据清洗过程
                print("\n=== 数据清洗过程 ===")
                
                # 统计清洗前的数据
                before_cleaning = len(df)
                print(f"清洗前数据行数: {before_cleaning}")
                
                # 1. 检查title列的空值情况
                title_na_count = df['title'].isna().sum()
                title_empty_count = (df['title'].astype(str).str.strip() == '').sum()
                print(f"标题列空值数量: {title_na_count}")
                print(f"标题列空字符串数量: {title_empty_count}")
                
                # 2. 检查Abstract Note列的空值情况  
                abstract_na_count = df['Abstract Note'].isna().sum()
                abstract_empty_count = (df['Abstract Note'].astype(str).str.strip() == '').sum()
                print(f"摘要列空值数量: {abstract_na_count}")
                print(f"摘要列空字符串数量: {abstract_empty_count}")
                
                # 3. 过滤掉标题或摘要为空的行
                df_cleaned = df.dropna(subset=['title', 'Abstract Note'])
                after_dropna = len(df_cleaned)
                print(f"删除空值后数据行数: {after_dropna}")
                
                # 4. 移除标题和摘要为空字符串的行
                df_cleaned = df_cleaned[
                    (df_cleaned['title'].astype(str).str.strip() != '') & 
                    (df_cleaned['Abstract Note'].astype(str).str.strip() != '')
                ]
                after_cleaning = len(df_cleaned)
                print(f"删除空字符串后数据行数: {after_cleaning}")
                
                # 计算被过滤的行数
                filtered_rows = before_cleaning - after_cleaning
                print(f"总共过滤掉的行数: {filtered_rows}")
                
                self.data = df_cleaned.to_dict('records')
                self.file_path = file_path
                
                self.file_label.setText(f"已导入 {len(self.data)} 条记录 - {os.path.basename(file_path)}")
                self.file_label.setStyleSheet("color: #27ae60; font-weight: bold;")
                
                # 初始化行数范围控件
                self.start_row_spin.setMaximum(len(self.data))
                self.end_row_spin.setMaximum(len(self.data))
                self.end_row_spin.setValue(len(self.data))
                self.all_rows_btn.setEnabled(True)
                
                # 更新范围和批次信息
                self.update_range_info()
                
                # 显示详细的导入报告
                report_text = (
                    f"文件导入成功！\n\n"
                    f"📁 文件编码: {used_encoding}\n"
                    f"📊 原始数据: {original_rows} 行 (含标题行)\n"
                    f"📋 有效数据: {original_rows - 1} 行\n"
                    f"✅ 最终导入: {len(self.data)} 条记录\n"
                    f"🗑️ 过滤掉: {filtered_rows} 条记录\n\n"
                    f"列名映射:\n"
                    f"• 标题列: '{title_col}' → 'title'\n"
                    f"• 摘要列: '{abstract_col}' → 'Abstract Note'\n\n"
                    f"过滤原因:\n"
                    f"• 标题为空: {title_na_count + title_empty_count} 条\n"
                    f"• 摘要为空: {abstract_na_count + abstract_empty_count} 条"
                )
                
                QMessageBox.information(self, "导入报告", report_text)
                
                self.run_btn.setEnabled(True)
                self.test_btn.setEnabled(True)
                self.update_batch_info()
                
            except Exception as e:
                error_msg = f"无法读取文件：{str(e)}"
                if "codec" in str(e).lower() or "encoding" in str(e).lower():
                    error_msg += "\n\n编码问题解决方案:\n"
                    error_msg += "1. 用Excel打开CSV文件，选择'另存为'\n"
                    error_msg += "2. 选择'UTF-8 CSV'格式保存\n"
                    error_msg += "3. 或将文件转换为Excel格式(.xlsx)"
                
                QMessageBox.critical(self, "导入失败", error_msg)
                
    def update_batch_info(self):
        """更新批次信息"""
        if self.data:
            # 获取处理范围
            start_row = self.start_row_spin.value()
            end_row = self.end_row_spin.value()
            
            # 确保范围有效
            start_row = max(1, start_row)
            end_row = min(len(self.data), end_row)
            
            if start_row > end_row:
                self.batch_info_label.setText("起始行不能大于结束行")
                self.batch_info_label.setStyleSheet("color: #e74c3c; font-weight: bold;")
                return
            
            # 计算实际要处理的记录数
            range_count = end_row - start_row + 1
            batch_count = self.batch_count_spin.value()
            batch_size = max(1, range_count // batch_count)
            
            # 计算最后一个批次的大小
            last_batch_size = range_count - (batch_count - 1) * batch_size
            
            if last_batch_size == batch_size:
                # 所有批次大小相等
                self.batch_info_label.setText(
                    f"将处理第{start_row}-{end_row}行（共{range_count}条记录），分为{batch_count}批次，每批{batch_size}条"
                )
            else:
                # 最后一个批次大小不同
                self.batch_info_label.setText(
                    f"将处理第{start_row}-{end_row}行（共{range_count}条记录），分为{batch_count}批次，前{batch_count-1}批每批{batch_size}条，最后一批{last_batch_size}条"
                )
            
            # 重置样式
            self.batch_info_label.setStyleSheet("color: #27ae60; font-weight: bold;")
        
    def update_range_info(self):
        """更新范围信息"""
        if self.data:
            total_count = len(self.data)
            start_row = self.start_row_spin.value()
            end_row = self.end_row_spin.value()
            
            # 确保范围在有效范围内
            if start_row > total_count:
                start_row = total_count
                self.start_row_spin.setValue(start_row)
            
            if end_row > total_count:
                end_row = total_count
                self.end_row_spin.setValue(end_row)
            
            if start_row > end_row:
                self.range_info_label.setText(f"❌ 无效范围：起始行({start_row}) > 结束行({end_row})")
                self.range_info_label.setStyleSheet("color: #e74c3c; font-weight: bold;")
                # 禁用运行按钮
                self.run_btn.setEnabled(False)
                self.test_btn.setEnabled(False)
            else:
                range_count = end_row - start_row + 1
                self.range_info_label.setText(f"✅ 有效范围：第{start_row}-{end_row}行，共{range_count}条记录 (总共{total_count}条)")
                self.range_info_label.setStyleSheet("color: #27ae60; font-weight: bold;")
                # 重新启用运行按钮
                self.run_btn.setEnabled(True)
                self.test_btn.setEnabled(True)
        
        # 更新批次信息
        self.update_batch_info()
    
    def set_all_rows(self):
        """设置为处理全部行"""
        if self.data:
            self.start_row_spin.setValue(1)
            self.end_row_spin.setValue(len(self.data))
    
    def test_screening(self):
        """测试运行 - 处理前5条记录"""
        if not self.data:
            QMessageBox.warning(self, "提示", "请先导入文献文件")
            return
        
        # 检查范围有效性
        start_row = self.start_row_spin.value()
        end_row = self.end_row_spin.value()
        if start_row > end_row:
            QMessageBox.warning(self, "提示", "起始行不能大于结束行")
            return
            
        # 获取当前配置
        platform_key = self.platform_combo.currentData()
        platform_config = AI_PLATFORMS[platform_key].copy()
        platform_config['model'] = self.model_combo.currentText()
        
        # 使用用户输入的API Key（如果有的话）
        user_api_key = self.api_key_input.text().strip()
        if user_api_key:
            platform_config['api_key'] = user_api_key
        
        prompt = self.prompt_text.toPlainText().strip()
        if not prompt:
            QMessageBox.warning(self, "提示", "请输入提示词")
            return
        
        # 从指定范围中取前5条进行测试
        range_data = self.data[start_row-1:end_row]  # 转换为0基索引
        test_data = range_data[:5]  # 取前5条
        
        # 确认测试运行
        reply = QMessageBox.question(
            self, "确认测试运行", 
            f"即将测试处理前5条记录（从第{start_row}行开始）。\n\n"
            f"AI平台: {AI_PLATFORMS[platform_key]['name']}\n"
            f"模型: {platform_config['model']}\n"
            f"Max Tokens: {self.max_tokens_input.value()}\n"
            f"Temperature: {self.temperature_input.value()}\n"
            f"API Key: {'用户自定义' if user_api_key else '使用预设'}\n"
            f"处理范围: 第{start_row}-{end_row}行（测试前5条）\n\n"
            f"是否继续？",
            QMessageBox.StandardButton.Yes | QMessageBox.StandardButton.No
        )
        
        if reply == QMessageBox.StandardButton.No:
            return
        
        # 在开始前创建测试文件
        try:
            base_name = os.path.splitext(self.file_path)[0]
            extension = os.path.splitext(self.file_path)[1]
            test_output_path = f"{base_name}-TEST-R{start_row}-{end_row}{extension}"
            
            # 创建初始的空DataFrame
            df = pd.DataFrame(columns=['序号', 'title', 'Abstract Note', '符合度&判断理由'])
            
            # 保存初始测试文件
            if extension.lower() == '.csv':
                df.to_csv(test_output_path, index=False, encoding='utf-8-sig')
            else:
                df.to_excel(test_output_path, index=False)
            
            print(f"✓ 已创建测试文件: {test_output_path}")
        except Exception as e:
            QMessageBox.warning(self, "警告", f"创建测试文件失败: {str(e)}")
            
        # 禁用控件
        self.test_btn.setEnabled(False)
        self.run_btn.setEnabled(False)
        self.stop_btn.setEnabled(True)
        self.import_btn.setEnabled(False)
        self.export_btn.setEnabled(False)
        
        # 清空处理过程显示框
        self.process_text.setPlainText("开始测试运行...\n\n")
        
        # 显示进度条
        self.main_progress.setVisible(True)
        self.main_progress.setMaximum(len(test_data))
        self.main_progress.setValue(0)
        
        # 创建工作线程
        self.worker = ProgressWorker()
        self.thread = QThread()
        self.worker.moveToThread(self.thread)
        
        # 连接信号
        self.worker.progress_updated.connect(self.update_progress)
        self.worker.result_ready.connect(self.process_test_results)
        self.worker.error_occurred.connect(self.handle_error)
        self.worker.finished.connect(self.task_finished)
        self.worker.process_info_updated.connect(self.update_process_info)  # 连接处理过程信号
        self.thread.started.connect(self.worker.run)
        
        # 设置测试参数
        self.worker.setup(
            test_data,
            platform_config,
            prompt,
            1,  # 只用1个批次
            self.max_tokens_input.value(),
            self.temperature_input.value()
        )
        self.worker.is_test_mode = True  # 标记为测试模式
        self.worker.set_auto_save(True, test_output_path)  # 设置测试文件保存路径
        self.worker.start_row_offset = start_row - 1  # 设置起始行偏移
        
        self.thread.start()
    
    def process_test_results(self, data):
        """处理测试结果"""
        test_results = data["results"]
        
        # 创建测试结果报告
        report = "🧪 测试运行完成！\n\n"
        report += f"✅ 成功处理: {len(test_results)} 条记录\n\n"
        
        # 显示每条结果的简要信息
        for i, result in enumerate(test_results):
            report += f"记录 {i+1}:\n"
            report += f"  标题: {result['title'][:50]}{'...' if len(result['title']) > 50 else ''}\n"
            ai_eval = result.get('ai_evaluation', '无评估结果')
            report += f"  AI评估: {ai_eval[:100]}{'...' if len(ai_eval) > 100 else ''}\n"
            report += f"  状态: {'✅ 成功' if ai_eval and '处理异常' not in ai_eval and 'API调用失败' not in ai_eval else '❌ 失败'}\n\n"
        
        # 获取测试文件路径
        try:
            base_name = os.path.splitext(self.file_path)[0]
            extension = os.path.splitext(self.file_path)[1]
            test_output_path = f"{base_name}-TEST{extension}"
            report += f"📁 测试结果已实时保存到: {test_output_path}\n\n"
            
            # 自动打开测试文件
            self.open_file_automatically(test_output_path)
            
        except Exception as e:
            report += f"⚠️ 获取测试文件路径失败: {str(e)}\n\n"
        
        report += "如果测试结果正常，可以点击'正式运行'开始处理全部数据。"
        
        QMessageBox.information(self, "测试结果", report)
        
        # 计算测试用时
        if self.worker and self.worker.start_time:
            elapsed_seconds = int(time.time() - self.worker.start_time)
            elapsed_time_str = self.format_elapsed_time(elapsed_seconds)
            self.status_label.setText(f"🧪 测试完成！可以进行正式运行 ⏱️ 测试用时：{elapsed_time_str}")
        else:
            self.status_label.setText("🧪 测试完成！可以进行正式运行")
        
    def run_screening(self):
        """运行AI筛选"""
        if not self.data:
            QMessageBox.warning(self, "提示", "请先导入文献文件")
            return
        
        # 检查范围有效性
        start_row = self.start_row_spin.value()
        end_row = self.end_row_spin.value()
        if start_row > end_row:
            QMessageBox.warning(self, "提示", "起始行不能大于结束行")
            return
            
        # 获取当前配置
        platform_key = self.platform_combo.currentData()
        platform_config = AI_PLATFORMS[platform_key].copy()
        platform_config['model'] = self.model_combo.currentText()
        
        # 使用用户输入的API Key（如果有的话）
        user_api_key = self.api_key_input.text().strip()
        if user_api_key:
            platform_config['api_key'] = user_api_key
        
        prompt = self.prompt_text.toPlainText().strip()
        if not prompt:
            QMessageBox.warning(self, "提示", "请输入提示词")
            return
        
        # 获取指定范围的数据
        range_data = self.data[start_row-1:end_row]  # 转换为0基索引
        range_count = len(range_data)
        
        # 确认正式运行
        reply = QMessageBox.question(
            self, "确认正式运行", 
            f"即将正式处理第{start_row}-{end_row}行的数据。\n\n"
            f"AI平台: {AI_PLATFORMS[platform_key]['name']}\n"
            f"模型: {platform_config['model']}\n"
            f"处理范围: 第{start_row}-{end_row}行（共{range_count}条记录）\n"
            f"批次数: {self.batch_count_spin.value()}\n"
            f"实时保存: {'启用' if self.auto_save_checkbox.isChecked() else '禁用'}\n"
            f"API Key: {'用户自定义' if user_api_key else '使用预设'}\n\n"
            f"预计耗时: {self.estimate_time(range_count)} 分钟\n\n"
            f"是否继续？",
            QMessageBox.StandardButton.Yes | QMessageBox.StandardButton.No
        )
        
        if reply == QMessageBox.StandardButton.No:
            return
            
        # 禁用控件
        self.run_btn.setEnabled(False)
        self.test_btn.setEnabled(False)
        self.stop_btn.setEnabled(True)
        self.import_btn.setEnabled(False)
        self.export_btn.setEnabled(False)
        
        # 清空处理过程显示框
        self.process_text.setPlainText("开始正式运行...\n\n")
        
        # 显示进度条
        self.main_progress.setVisible(True)
        self.main_progress.setMaximum(range_count)
        self.main_progress.setValue(0)
        
        # 创建工作线程
        self.worker = ProgressWorker()
        self.thread = QThread()
        self.worker.moveToThread(self.thread)
        
        # 连接信号
        self.worker.progress_updated.connect(self.update_progress)
        self.worker.result_ready.connect(self.process_results)
        self.worker.error_occurred.connect(self.handle_error)
        self.worker.finished.connect(self.task_finished)
        self.worker.batch_completed.connect(self.on_batch_completed)
        self.worker.process_info_updated.connect(self.update_process_info)  # 连接处理过程信号
        self.thread.started.connect(self.worker.run)
        
        # 设置实时保存参数
        if self.auto_save_checkbox.isChecked():
            base_name = os.path.splitext(self.file_path)[0]
            extension = os.path.splitext(self.file_path)[1]
            output_path_base = f"{base_name}-R{start_row}-{end_row}{extension}"
            self.worker.set_auto_save(True, output_path_base)
        else:
            self.worker.set_auto_save(False)
        
        # 设置参数并启动
        self.worker.setup(
            range_data,
            platform_config,
            prompt,
            self.batch_count_spin.value(),
            self.max_tokens_input.value(),
            self.temperature_input.value()
        )
        self.worker.start_row_offset = start_row - 1  # 设置起始行偏移
        
        self.thread.start()
    
    def estimate_time(self, record_count=None):
        """估算处理时间"""
        if record_count is None:
            record_count = len(self.data) if self.data else 0
        # 每条记录大约需要1秒 + 0.5秒延时 = 1.5秒
        total_seconds = record_count * 1.5
        return int(total_seconds / 60) + 1
    
    def format_elapsed_time(self, elapsed_seconds):
        """格式化显示用时信息"""
        if elapsed_seconds < 60:
            return f"{elapsed_seconds}秒"
        elif elapsed_seconds < 3600:
            minutes = elapsed_seconds // 60
            seconds = elapsed_seconds % 60
            return f"{minutes}分{seconds}秒"
        else:
            hours = elapsed_seconds // 3600
            minutes = (elapsed_seconds % 3600) // 60
            seconds = elapsed_seconds % 60
            return f"{hours}小时{minutes}分{seconds}秒"
    
    def on_batch_completed(self, batch_number, batch_data, elapsed_seconds):
        """批次完成处理"""
        batch_results = batch_data["results"]
        
        # 计算总的已处理记录数
        # 这里需要累积到当前批次为止的所有记录数
        total_count = len(self.data) if self.data else 0
        total_batches = self.batch_count_spin.value()
        batch_size = max(1, total_count // total_batches)
        
        # 计算到当前批次为止已处理的记录数
        if batch_number < total_batches:
            total_processed = batch_number * batch_size
        else:
            # 最后一个批次可能包含剩余的所有记录
            total_processed = (batch_number - 1) * batch_size + len(batch_results)
        
        # 计算成功和失败的记录数
        success_count = 0
        failure_count = 0
        for result in batch_results:
            ai_eval = result.get('ai_evaluation', '')
            if ai_eval and '处理异常' not in ai_eval and 'API调用失败' not in ai_eval and '连接异常' not in ai_eval:
                success_count += 1
            else:
                failure_count += 1
        
        # 格式化用时显示
        elapsed_time_str = self.format_elapsed_time(elapsed_seconds)
        
        # 使用新的格式显示批次完成状态，添加用时信息
        # 格式保持与进度显示一致的风格，增加用时显示
        status_text = f"✅ 第{batch_number}批完成（此批{len(batch_results)}条）/共{total_batches}批/共{total_count}条 - 已处理{total_processed}条 ⏱️ {elapsed_time_str}"
        if failure_count > 0:
            status_text += f" (成功: {success_count}, 失败: {failure_count})"
        
        self.status_label.setText(status_text)
        
        # 如果启用了实时保存，显示保存信息
        if self.auto_save_checkbox.isChecked():
            save_info = f"💾 第 {batch_number}/{total_batches} 批次结果已自动保存"
            if failure_count > 0:
                save_info += f" (包含 {failure_count} 条异常记录)"
            print(save_info)
            
            # 在处理过程框中也显示批次完成信息
            batch_info = f"\n🎯 第{batch_number}批处理完成（此批{len(batch_results)}条）/共{total_batches}批/共{total_count}条！\n"
            batch_info += f"   ✅ 成功处理: {success_count} 条\n"
            if failure_count > 0:
                batch_info += f"   ❌ 处理失败: {failure_count} 条\n"
            batch_info += f"   💾 结果已自动保存到文件\n"
            batch_info += f"   📊 总进度: {total_processed}/{total_count} ({int(total_processed/total_count*100)}%)\n"
            batch_info += f"{'='*50}\n\n"
            
            self.process_text.append(batch_info)
    
    def stop_screening(self):
        """中止筛选"""
        if self.worker:
            self.worker.stop()
            self.status_label.setText("正在中止...")
            
    def update_progress(self, current, total, batch_current, batch_total, status, elapsed_seconds):
        """更新进度"""
        self.main_progress.setValue(current)
        progress_percent = int((current / total) * 100)
        
        # 获取总批次数
        total_batches = self.batch_count_spin.value()
        
        # 计算当前批次编号（基于current和batch_size）
        if total > 0:
            batch_size = max(1, total // total_batches)
            current_batch = min(((current - 1) // batch_size) + 1, total_batches)
        else:
            current_batch = 1
        
        # 格式化用时显示
        elapsed_time_str = self.format_elapsed_time(elapsed_seconds)
        
        # 按照用户要求的格式显示进度信息，增加用时信息
        # 格式：正处理第20条/正处理第2批（此批100条）/共5批/共1300条 (50%) ⏱️ 2分30秒 | 状态
        status_text = f"正处理第{current}条/正处理第{current_batch}批（此批{batch_total}条）/共{total_batches}批/共{total}条 ({progress_percent}%) ⏱️ {elapsed_time_str} | {status}"
        self.status_label.setText(status_text)
        
    def process_results(self, data):
        """处理结果"""
        self.results = data["results"]
        
        # 如果有worker，获取用时信息
        if self.worker and self.worker.start_time:
            elapsed_seconds = int(time.time() - self.worker.start_time)
            elapsed_time_str = self.format_elapsed_time(elapsed_seconds)
            self.status_label.setText(f"✅ 处理完成！共处理 {len(self.results)} 条记录 ⏱️ 总用时：{elapsed_time_str}")
        else:
            self.status_label.setText(f"✅ 处理完成！共处理 {len(self.results)} 条记录")
            
        self.export_btn.setEnabled(True)
        
        # 如果是正式运行（不是测试），自动保存并打开结果文件
        if not self.worker.is_test_mode:
            try:
                # 准备导出数据
                export_data = []
                for i, result in enumerate(self.results):
                    # 直接使用AI的完整评估结果
                    ai_evaluation = result.get('ai_evaluation', '无评估结果')
                    
                    # 计算实际行号（考虑起始行偏移）
                    actual_row_number = (self.worker.start_row_offset if self.worker else 0) + i + 1
                    
                    export_data.append({
                        '序号': actual_row_number,
                        'title': result['title'],
                        'Abstract Note': result['abstract'],
                        '符合度&判断理由': ai_evaluation
                    })
                
                # 创建DataFrame
                df = pd.DataFrame(export_data)
                
                # 生成输出文件名
                base_name = os.path.splitext(self.file_path)[0]
                extension = os.path.splitext(self.file_path)[1]
                if self.worker and hasattr(self.worker, 'start_row_offset'):
                    start_row = self.worker.start_row_offset + 1
                    end_row = self.worker.start_row_offset + len(self.results)
                    output_path = f"{base_name}-R{start_row}-{end_row}{extension}"
                else:
                    output_path = f"{base_name}-R{extension}"
                
                # 保存文件
                if extension.lower() == '.csv':
                    df.to_csv(output_path, index=False, encoding='utf-8-sig')
                else:
                    df.to_excel(output_path, index=False)
                
                # 更新状态，包含用时信息
                if self.worker and self.worker.start_time:
                    elapsed_seconds = int(time.time() - self.worker.start_time)
                    elapsed_time_str = self.format_elapsed_time(elapsed_seconds)
                    self.status_label.setText(f"✅ 处理完成！共处理 {len(self.results)} 条记录，结果已自动保存 ⏱️ 总用时：{elapsed_time_str}")
                else:
                    self.status_label.setText(f"✅ 处理完成！共处理 {len(self.results)} 条记录，结果已自动保存")
                
                # 直接自动打开结果文件，不显示成功提示弹窗
                self.open_file_automatically(output_path)
                
            except Exception as e:
                QMessageBox.critical(self, "自动保存失败", f"无法保存文件：{str(e)}")
                print(f"自动保存失败: {e}")
    
    def handle_error(self, error_msg):
        """处理错误"""
        QMessageBox.critical(self, "错误", error_msg)
        self.status_label.setText(f"处理失败: {error_msg}")
        
    def task_finished(self):
        """任务完成"""
        # 重新启用控件
        self.run_btn.setEnabled(True)
        self.test_btn.setEnabled(True)  # 重新启用测试按钮
        self.stop_btn.setEnabled(False)
        self.import_btn.setEnabled(True)
        
        # 在处理过程框中显示完成信息
        self.process_text.append("🎉 任务完成！")
        
        # 清理线程
        if self.thread:
            self.thread.quit()
            self.thread.wait()
            self.thread = None
            self.worker = None
            
    def export_results(self):
        """导出结果"""
        if not self.results:
            QMessageBox.warning(self, "提示", "没有结果可导出")
            return
            
        try:
            # 准备导出数据
            export_data = []
            for i, result in enumerate(self.results):
                # 直接使用AI的完整评估结果
                ai_evaluation = result.get('ai_evaluation', '无评估结果')
                
                export_data.append({
                    '序号': i + 1,
                    'title': result['title'],
                    'Abstract Note': result['abstract'],
                    '符合度&判断理由': ai_evaluation
                })
            
            # 创建DataFrame
            df = pd.DataFrame(export_data)
            
            # 生成输出文件名
            base_name = os.path.splitext(self.file_path)[0]
            extension = os.path.splitext(self.file_path)[1]
            output_path = f"{base_name}-R{extension}"
            
            # 保存文件
            if extension.lower() == '.csv':
                df.to_csv(output_path, index=False, encoding='utf-8-sig')
            else:
                df.to_excel(output_path, index=False)
            
            # 直接自动打开结果文件，不显示成功提示弹窗
            self.open_file_automatically(output_path)
            
        except Exception as e:
            QMessageBox.critical(self, "导出失败", f"无法保存文件：{str(e)}")

    def import_result_file(self):
        """导入结果文件"""
        file_path, _ = QFileDialog.getOpenFileName(
            self, "选择结果文件", "", 
            "Excel/CSV Files (*.xlsx *.xls *.csv)"
        )
        
        if file_path:
            try:
                # 根据文件扩展名选择读取方法
                if file_path.endswith('.csv'):
                    # 自动检测CSV文件编码
                    encodings = ['utf-8', 'gbk', 'gb2312', 'utf-8-sig', 'latin1', 'cp1252']
                    df = None
                    used_encoding = None
                    
                    for encoding in encodings:
                        try:
                            df = pd.read_csv(file_path, encoding=encoding)
                            used_encoding = encoding
                            print(f"✓ 成功使用 {encoding} 编码读取结果文件")
                            break
                        except UnicodeDecodeError:
                            print(f"✗ {encoding} 编码读取失败，尝试下一个...")
                            continue
                        except Exception as e:
                            print(f"✗ 使用 {encoding} 编码时发生其他错误: {e}")
                            continue
                    
                    if df is None:
                        QMessageBox.critical(
                            self, "编码错误", 
                            "无法识别文件编码格式。\n\n"
                            "请尝试：\n"
                            "1. 用Excel打开CSV文件，另存为UTF-8格式\n"
                            "2. 或将文件转换为Excel格式(.xlsx)"
                        )
                        return
                else:
                    df = pd.read_excel(file_path)
                    used_encoding = "Excel格式"
                
                # 检查是否包含必需的列
                if '符合度&判断理由' not in df.columns:
                    QMessageBox.warning(
                        self, "文件格式错误", 
                        f"结果文件中未找到'符合度&判断理由'列。\n\n"
                        f"文件中的列名: {', '.join(df.columns)}\n\n"
                        f"请确保导入的是AI文献筛选工具生成的结果文件。"
                    )
                    return
                
                # 成功导入
                self.result_file_path = file_path
                
                record_count = len(df)
                self.result_file_label.setText(f"已导入 {record_count} 条记录 - {os.path.basename(file_path)}")
                self.result_file_label.setStyleSheet("color: #27ae60; font-weight: bold;")
                
                # 启用处理按钮
                self.process_result_btn.setEnabled(True)
                
                # 显示导入成功信息
                QMessageBox.information(
                    self, "导入成功", 
                    f"✅ 结果文件导入成功！\n\n"
                    f"📁 文件编码: {used_encoding}\n"
                    f"📊 记录数量: {record_count} 条\n"
                    f"📋 包含列: {len(df.columns)} 列\n\n"
                    f"现在可以点击'运行增加评估数值列'来处理文件。"
                )
                
            except Exception as e:
                error_msg = f"无法读取结果文件：{str(e)}"
                QMessageBox.critical(self, "导入失败", error_msg)
                print(f"导入结果文件失败: {e}")

    def process_result_file(self):
        """处理结果文件，添加评估数值列"""
        if not self.result_file_path:
            QMessageBox.warning(self, "提示", "请先导入结果文件")
            return
        
        try:
            # 读取结果文件
            if self.result_file_path.endswith('.csv'):
                # 自动检测CSV文件编码
                encodings = ['utf-8', 'gbk', 'gb2312', 'utf-8-sig', 'latin1', 'cp1252']
                df = None
                
                for encoding in encodings:
                    try:
                        df = pd.read_csv(self.result_file_path, encoding=encoding)
                        break
                    except UnicodeDecodeError:
                        continue
                    except Exception as e:
                        continue
                
                if df is None:
                    QMessageBox.critical(self, "读取失败", "无法读取结果文件")
                    return
            else:
                df = pd.read_excel(self.result_file_path)
            
            # 提取符合度数值
            evaluation_results = []
            for index, row in df.iterrows():
                evaluation_text = str(row.get('符合度&判断理由', ''))
                extracted_value = self.extract_percentage(evaluation_text)
                evaluation_results.append(extracted_value)
            
            # 在'符合度&判断理由'列后面添加新列
            columns_list = df.columns.tolist()
            target_col_index = columns_list.index('符合度&判断理由')
            
            # 创建新的DataFrame，插入新列
            new_df = df.copy()
            new_df.insert(target_col_index + 1, '符合度评估结果', evaluation_results)
            
            # 生成输出文件名
            base_name = os.path.splitext(self.result_file_path)[0]
            extension = os.path.splitext(self.result_file_path)[1]
            output_path = f"{base_name}+评估数值列{extension}"
            
            # 保存文件
            if extension.lower() == '.csv':
                new_df.to_csv(output_path, index=False, encoding='utf-8-sig')
            else:
                new_df.to_excel(output_path, index=False)
            
            # 统计处理结果
            successful_extractions = sum(1 for val in evaluation_results if val != '无法提取')
            
            # 显示处理结果
            result_msg = (
                f"🎉 处理完成！\n\n"
                f"📊 总记录数：{len(df)} 条\n"
                f"✅ 成功提取：{successful_extractions} 条\n"
                f"❌ 无法提取：{len(df) - successful_extractions} 条\n\n"
                f"📁 输出文件：{os.path.basename(output_path)}\n\n"
                f"新增的'符合度评估结果'列已添加到原数据右侧。"
            )
            
            QMessageBox.information(self, "处理完成", result_msg)
            
            # 自动打开生成的文件
            self.open_file_automatically(output_path)
            
        except Exception as e:
            error_msg = f"处理结果文件时发生错误：{str(e)}"
            QMessageBox.critical(self, "处理失败", error_msg)
            print(f"处理结果文件失败: {e}")
    
    def extract_percentage(self, text):
        """从文本中提取第一个百分比数值"""
        import re
        
        # 使用正则表达式匹配百分比
        # 匹配模式：数字(可含小数点) + %
        # 支持各种格式：85%、85.5%、100%等
        pattern = r'(\d+(?:\.\d+)?%)'
        matches = re.findall(pattern, text)
        
        if matches:
            # 返回第一个匹配的百分比
            return matches[0]
        else:
            # 如果没有找到百分比，尝试查找"符合度评估结果："后的数值
            # 处理可能的格式变化
            result_pattern = r'符合度评估结果[：:]\s*(\d+(?:\.\d+)?%)'
            result_matches = re.findall(result_pattern, text)
            if result_matches:
                return result_matches[0]
            
            # 如果仍然没有找到，返回提示信息
            return '无法提取'

    def open_file_automatically(self, file_path):
        """自动打开结果文件"""
        try:
            if platform.system() == 'Windows':
                os.startfile(file_path)
            elif platform.system() == 'Darwin':  # macOS
                subprocess.run(['open', file_path])
            else:  # Linux
                subprocess.run(['xdg-open', file_path])
            print(f"✓ 已自动打开文件: {file_path}")
        except Exception as e:
            print(f"自动打开文件失败: {e}")
            # 如果自动打开失败，显示文件路径
            QMessageBox.information(
                self, "文件已保存", 
                f"结果文件已保存到：\n{file_path}\n\n请手动打开查看。"
            )

    def update_process_info(self, info):
        """更新处理过程信息"""
        # 获取现有内容
        current_text = self.process_text.toPlainText()
        
        # 如果是开始处理新记录（包含分隔线），则不追加而是替换最近的记录信息
        if "📤 开始处理新记录" in info and "=" in info:
            # 查找上一个记录的结束位置
            if "📤 开始处理新记录" in current_text:
                # 如果已经有处理记录，保留前面完成的记录，清空当前正在处理的
                lines = current_text.split('\n')
                last_complete_index = -1
                for i, line in enumerate(lines):
                    if "⏰ 完成时间：" in line and "=" in lines[i+1:i+3]:
                        last_complete_index = i + 2  # 包含分隔线
                
                if last_complete_index > 0:
                    # 保留已完成的记录
                    completed_records = '\n'.join(lines[:last_complete_index]) + '\n\n'
                    self.process_text.setPlainText(completed_records + info)
                else:
                    # 第一次处理记录
                    if current_text.strip() in ["等待开始处理...", "开始测试运行...", "开始正式运行..."]:
                        self.process_text.setPlainText(info)
                    else:
                        self.process_text.setPlainText(current_text + info)
            else:
                # 第一次处理记录
                if current_text.strip() in ["等待开始处理...", "开始测试运行...", "开始正式运行..."]:
                    self.process_text.setPlainText(info)
                else:
                    self.process_text.setPlainText(current_text + info)
        else:
            # 追加其他信息（API调用状态、响应等）
            self.process_text.append(info)
        
        # 自动滚动到底部显示最新内容
        scrollbar = self.process_text.verticalScrollBar()
        scrollbar.setValue(scrollbar.maximum())

def main():
    """主函数"""
    app = QApplication(sys.argv)
    app.setApplicationName("AI文献筛选工具")
    app.setApplicationVersion("1.0.0")
    
    # 设置应用图标（如果有的话）
    # app.setWindowIcon(QIcon("icon.png"))
    
    window = AILiteratureScreeningTool()
    window.show()
    
    sys.exit(app.exec())

if __name__ == "__main__":
    main() 