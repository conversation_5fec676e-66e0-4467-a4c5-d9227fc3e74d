#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
SPIS+ScreenP 整合工具
整合了SPIS文献求助自动化工具和ScreenP AI文献筛选工具
保持两个工具功能的绝对独立性
"""

# ========================================
# SPIS 文献求助自动化工具部分
# ========================================

import pandas as pd
import time
from selenium import webdriver
from selenium.webdriver.common.by import By
from selenium.webdriver.chrome.service import Service
from selenium.webdriver.support.ui import WebDriverWait
from selenium.webdriver.support import expected_conditions as EC
import sys
import os
import random
from PyQt6.QtWidgets import *
from PyQt6.QtCore import *
from PyQt6.QtGui import *

# ========== SPIS 配置部分 ==========
SPIS_CSV_FILE = '导出的条目.csv'  # 原始CSV文件名
SPIS_XLSX_FILE = '导出的条目.xlsx'  # 转换后的Excel文件名
SPIS_BATCHED_XLSX_FILE = '带批次_导出的条目.xlsx'  # 新增批次列后的Excel文件名
SPIS_BATCH_SIZE = 50
SPIS_CHROME_DRIVER_PATH = 'chromedriver.exe'  # chromedriver路径
SPIS_WAIT_TIME = 15  # 页面元素最大等待秒数
# =============================

def spis_csv_to_xlsx(csv_file, xlsx_file):
    """SPIS工具：CSV转Excel功能"""
    # 自动检测编码和分隔符
    encodings = ['utf-8', 'gbk', 'gb2312', 'utf-16', 'latin-1']
    df = None
    
    for encoding in encodings:
        try:
            with open(csv_file, 'r', encoding=encoding) as f:
                first_line = f.readline()
                if ',' in first_line:
                    sep = ','
                elif ';' in first_line:
                    sep = ';'
                elif '\t' in first_line:
                    sep = '\t'
                else:
                    sep = ','
            
            df = pd.read_csv(csv_file, encoding=encoding, sep=sep)
            print(f'成功使用编码 {encoding} 读取文件')
            break
        except UnicodeDecodeError:
            continue
    
    if df is None:
        print('错误：无法使用常见编码格式读取CSV文件，请检查文件编码！')
        sys.exit(1)
    # 只保留Title和DOI两列
    keep_cols = []
    for col in df.columns:
        if col.strip().lower() == 'doi':
            keep_cols.append(col)
        if col.strip().lower() == 'title':
            keep_cols.append(col)
    if len(keep_cols) < 2:
        print('错误：原始文件中未找到DOI和Title两列，请检查文件内容！')
        sys.exit(1)
    df = df[keep_cols]
    # 对于DOI列为空的行，将Title列内容填入DOI列
    doi_col = [col for col in df.columns if col.strip().lower() == 'doi'][0]
    title_col = [col for col in df.columns if col.strip().lower() == 'title'][0]
    empty_doi_mask = df[doi_col].isna() | (df[doi_col].astype(str).str.strip() == '')
    df.loc[empty_doi_mask, doi_col] = df.loc[empty_doi_mask, title_col]
    df.to_excel(xlsx_file, index=False)
    print(f'已将{csv_file}仅保留DOI和Title两列（DOI为空时已用Title补全）并转换为Excel文件{xlsx_file}')
    return xlsx_file

def spis_add_order_and_batchN(xlsx_file, batched_xlsx_file):
    """SPIS工具：添加序号和批次号功能"""
    df = pd.read_excel(xlsx_file)
    if 'Title' not in df.columns:
        df['Title'] = ''
    # 新增order列（在DOI前）
    doi_loc = df.columns.get_loc('DOI')
    if isinstance(doi_loc, int):
        # 使用pandas Series创建order列数据
        order_series = pd.Series(range(1, len(df) + 1))
        df.insert(doi_loc, 'order', order_series)
        # 新增batchN列（在DOI后）
        df.insert(doi_loc + 1, 'batchN', '')
    # 只对有DOI的行分配批次编号
    doi_mask = df['DOI'].notnull() & (df['DOI'].astype(str).str.strip() != '')
    doi_indices = df.index[doi_mask].tolist()
    for i, idx in enumerate(doi_indices):
        batch_num = (i // SPIS_BATCH_SIZE) + 1
        df.at[idx, 'batchN'] = batch_num
    df.to_excel(batched_xlsx_file, index=False)
    # 分批统计
    batch_counts = df.loc[doi_mask, 'batchN'].value_counts().sort_index()
    total = doi_mask.sum()
    print(f'共计有DOI信息的文献：{total}篇')
    print(f'共分为{batch_counts.shape[0]}批')
    for batch, count in batch_counts.items():
        print(f'第{batch}批：{count}篇')
    return df, batch_counts.shape[0]

def spis_process_batch_xlsx(batch_num):
    """SPIS工具：处理指定批次的文献求助"""
    df = pd.read_excel(SPIS_BATCHED_XLSX_FILE)
    if 'Title' not in df.columns:
        df['Title'] = ''
    batch_df = df[
        (df['batchN'].notnull()) &
        (df['DOI'].notnull()) & (df['DOI'].astype(str).str.strip() != '') &
        (df['batchN'].apply(lambda x: str(int(x)) if pd.notnull(x) and str(x).strip() != '' else '') == str(batch_num))
    ]
    total = len(batch_df)
    # 调试输出：以Excel表格格式输出当前批次所有行
    print(f'实际读取到的第{batch_num}批次所有行内容（Excel表格格式）：')
    # 输出列名
    print('\t'.join(batch_df.columns))
    # 输出每行内容
    for _, row in batch_df.iterrows():
        row_str = '\t'.join([str(x) if len(str(x)) <= 30 else str(x)[:15] + '...' for x in row])
        print(row_str)
    if total == 0:
        print(f'第{batch_num}批无文献可处理（已自动跳过DOI和batchN为空或NaN的行）。')
        return
    print(f'开始处理第{batch_num}批，共{total}篇文献。请确保已手动登录目标网站并将浏览器窗口置于前台。')
    driver = webdriver.Chrome(service=Service(SPIS_CHROME_DRIVER_PATH))
    driver.get('https://spis.hnlat.com/scholar/list?val=10.1016%2Fj.ijhm.2025.104227&oaFirst=0')
    input("请手动登录目标网站并打开首页后，按回车键继续...")

    success, fail = 0, []
    start_time = time.time()
    for _, row in batch_df.iterrows():
        doi = str(row['DOI']).strip()
        title = str(row['Title']).strip()
        try:
            search_box = WebDriverWait(driver, SPIS_WAIT_TIME).until(
                EC.presence_of_element_located((By.CSS_SELECTOR, "input[type='text']"))
            )
            search_box.clear()
            search_box.send_keys(doi)
            print(f"请手动点击'搜文章'按钮，并在结果页手动点击第1篇论文下的'文献求助'按钮……")
            checkbox = WebDriverWait(driver, 600).until(
                EC.element_to_be_clickable((By.XPATH, "//input[@type='checkbox']"))
            )
            checkbox.click()
            print(f"已自动勾选服务条款，请手动点击'确定'按钮。点击后将自动进入下一个DOI。")
            WebDriverWait(driver, 600).until_not(
                EC.presence_of_element_located((By.XPATH, "//input[@type='checkbox']"))
            )
            success += 1
            print(f"已处理 {success}/{total} 篇，当前处理：{title}")
            time.sleep(random.uniform(1, 2))
        except Exception as e:
            fail.append((doi, title, str(e)))
            print(f"处理失败：{doi} - {title} - {str(e)}")
            continue

    elapsed = int(time.time() - start_time)
    print(f"\n第{batch_num}批次处理完成！")
    print(f"成功处理：{success} 篇")
    if fail:
        print("失败DOI及原因：")
        for doi, title, reason in fail:
            print(f"{doi} - {title} - {reason}")
    print(f"总耗时：{elapsed // 60}分{elapsed % 60}秒")
    driver.quit()

class EditableBatchDetailWindow(QMainWindow):
    """可编辑的批次详情窗口"""

    def __init__(self, batch_num, batch_data, parent=None):
        super().__init__(parent)
        self.batch_num = batch_num
        self.batch_data = batch_data.copy()  # 创建数据副本
        self.original_data = batch_data.copy()  # 保存原始数据
        self.parent_tool = parent
        self.init_ui()

    def init_ui(self):
        """初始化用户界面"""
        self.setWindowTitle(f"第{self.batch_num}批次详情 - 可编辑")
        self.setGeometry(300, 200, 1000, 700)
        self.setStyleSheet(self.get_detail_stylesheet())

        # 创建中央窗口
        central_widget = QWidget()
        self.setCentralWidget(central_widget)

        # 创建主布局
        main_layout = QVBoxLayout(central_widget)
        main_layout.setSpacing(10)
        main_layout.setContentsMargins(15, 15, 15, 15)

        # 标题和工具栏
        self.create_header(main_layout)

        # 可编辑表格
        self.create_editable_table(main_layout)

        # 底部按钮栏
        self.create_button_bar(main_layout)

    def get_detail_stylesheet(self):
        """获取详情窗口样式表"""
        return """
            QMainWindow {
                background-color: #f8f9fa;
            }
            QLabel#header {
                font-size: 18px;
                font-weight: bold;
                color: #e74c3c;
                padding: 10px;
                background-color: white;
                border-radius: 5px;
                border: 2px solid #e74c3c;
            }
            QTableWidget {
                background-color: white;
                border: 2px solid #e74c3c;
                border-radius: 5px;
                gridline-color: #dee2e6;
                font-size: 12px;
            }
            QTableWidget::item {
                padding: 8px;
                border-bottom: 1px solid #dee2e6;
            }
            QTableWidget::item:selected {
                background-color: #ffeaa7;
                color: #2d3436;
            }
            QHeaderView::section {
                background-color: #e74c3c;
                color: white;
                padding: 8px;
                border: none;
                font-weight: bold;
            }
            QPushButton {
                background-color: #e74c3c;
                border: none;
                color: white;
                padding: 10px 20px;
                font-size: 12px;
                border-radius: 5px;
                font-weight: bold;
                min-width: 100px;
            }
            QPushButton:hover {
                background-color: #c0392b;
                border: 1px solid #a93226;
            }
            QPushButton:pressed {
                background-color: #a93226;
            }
            QPushButton:disabled {
                background-color: #bdc3c7;
                color: #7f8c8d;
            }
            QPushButton#save-btn {
                background-color: #27ae60;
            }
            QPushButton#save-btn:hover {
                background-color: #229954;
            }
            QPushButton#add-btn {
                background-color: #3498db;
            }
            QPushButton#add-btn:hover {
                background-color: #2980b9;
            }
        """

    def create_header(self, parent_layout):
        """创建标题和工具栏"""
        header_layout = QHBoxLayout()

        # 标题信息
        header_label = QLabel(f"第{self.batch_num}批次详情 - 共{len(self.batch_data)}条记录")
        header_label.setObjectName("header")
        header_layout.addWidget(header_label)

        # 状态标签
        self.status_label = QLabel("就绪")
        self.status_label.setStyleSheet("color: #27ae60; font-weight: bold; padding: 10px;")
        header_layout.addWidget(self.status_label)

        header_layout.addStretch()
        parent_layout.addLayout(header_layout)

    def create_editable_table(self, parent_layout):
        """创建可编辑表格"""
        self.table = QTableWidget()
        self.table.setRowCount(len(self.batch_data))
        self.table.setColumnCount(len(self.batch_data.columns))
        self.table.setHorizontalHeaderLabels(self.batch_data.columns.tolist())

        # 填充数据
        for i, (_, row) in enumerate(self.batch_data.iterrows()):
            for j, value in enumerate(row):
                item = QTableWidgetItem(str(value))
                # 设置为可编辑
                item.setFlags(item.flags() | Qt.ItemFlag.ItemIsEditable)
                self.table.setItem(i, j, item)

        # 设置表格属性
        self.table.resizeColumnsToContents()
        self.table.setAlternatingRowColors(True)
        self.table.setSelectionBehavior(QAbstractItemView.SelectionBehavior.SelectRows)
        self.table.setSortingEnabled(True)

        # 连接数据变化信号
        self.table.itemChanged.connect(self.on_item_changed)

        parent_layout.addWidget(self.table)

    def create_button_bar(self, parent_layout):
        """创建底部按钮栏"""
        button_layout = QHBoxLayout()

        # 添加行按钮
        self.add_row_btn = QPushButton("➕ 添加行")
        self.add_row_btn.setObjectName("add-btn")
        self.add_row_btn.clicked.connect(self.add_row)
        self.add_row_btn.setAutoDefault(False)
        self.add_row_btn.setDefault(False)
        button_layout.addWidget(self.add_row_btn)

        # 删除行按钮
        self.delete_row_btn = QPushButton("➖ 删除选中行")
        self.delete_row_btn.clicked.connect(self.delete_selected_rows)
        self.delete_row_btn.setAutoDefault(False)
        self.delete_row_btn.setDefault(False)
        button_layout.addWidget(self.delete_row_btn)

        button_layout.addStretch()

        # 重置按钮
        self.reset_btn = QPushButton("🔄 重置")
        self.reset_btn.clicked.connect(self.reset_data)
        self.reset_btn.setAutoDefault(False)
        self.reset_btn.setDefault(False)
        button_layout.addWidget(self.reset_btn)

        # 保存按钮
        self.save_btn = QPushButton("💾 保存更改")
        self.save_btn.setObjectName("save-btn")
        self.save_btn.clicked.connect(self.save_changes)
        self.save_btn.setAutoDefault(False)
        self.save_btn.setDefault(False)
        button_layout.addWidget(self.save_btn)

        # 关闭按钮
        self.close_btn = QPushButton("❌ 关闭")
        self.close_btn.clicked.connect(self.close)
        self.close_btn.setAutoDefault(False)
        self.close_btn.setDefault(False)
        button_layout.addWidget(self.close_btn)

        parent_layout.addLayout(button_layout)

    def on_item_changed(self, item):
        """表格项目改变时的处理"""
        self.status_label.setText("数据已修改 - 请保存更改")
        self.status_label.setStyleSheet("color: #f39c12; font-weight: bold; padding: 10px;")

    def add_row(self):
        """添加新行"""
        current_row_count = self.table.rowCount()
        self.table.insertRow(current_row_count)

        # 为新行设置默认值
        columns = [self.table.horizontalHeaderItem(i).text() if self.table.horizontalHeaderItem(i) else f"Column_{i}" for i in range(self.table.columnCount())]
        for j, col_name in enumerate(columns):
            if col_name.lower() == 'order':
                item = QTableWidgetItem(str(current_row_count + 1))
            elif col_name.lower() == 'batchn':
                item = QTableWidgetItem(str(self.batch_num))
            else:
                item = QTableWidgetItem("")

            item.setFlags(item.flags() | Qt.ItemFlag.ItemIsEditable)
            self.table.setItem(current_row_count, j, item)

        self.status_label.setText(f"已添加新行 - 请填写数据并保存")
        self.status_label.setStyleSheet("color: #3498db; font-weight: bold; padding: 10px;")

    def delete_selected_rows(self):
        """删除选中的行"""
        selected_rows = set()
        for item in self.table.selectedItems():
            selected_rows.add(item.row())

        if not selected_rows:
            QMessageBox.warning(self, "删除行", "请先选择要删除的行")
            return

        reply = QMessageBox.question(
            self, "确认删除",
            f"确定要删除选中的 {len(selected_rows)} 行吗？",
            QMessageBox.StandardButton.Yes | QMessageBox.StandardButton.No
        )

        if reply == QMessageBox.StandardButton.Yes:
            # 从后往前删除，避免索引变化
            for row in sorted(selected_rows, reverse=True):
                self.table.removeRow(row)

            self.status_label.setText(f"已删除 {len(selected_rows)} 行 - 请保存更改")
            self.status_label.setStyleSheet("color: #e74c3c; font-weight: bold; padding: 10px;")

    def reset_data(self):
        """重置数据到原始状态"""
        reply = QMessageBox.question(
            self, "确认重置",
            "确定要重置所有更改吗？这将丢失所有未保存的修改。",
            QMessageBox.StandardButton.Yes | QMessageBox.StandardButton.No
        )

        if reply == QMessageBox.StandardButton.Yes:
            self.batch_data = self.original_data.copy()
            self.refresh_table()
            self.status_label.setText("数据已重置")
            self.status_label.setStyleSheet("color: #27ae60; font-weight: bold; padding: 10px;")

    def refresh_table(self):
        """刷新表格显示"""
        self.table.setRowCount(len(self.batch_data))

        for i, (_, row) in enumerate(self.batch_data.iterrows()):
            for j, value in enumerate(row):
                item = QTableWidgetItem(str(value))
                item.setFlags(item.flags() | Qt.ItemFlag.ItemIsEditable)
                self.table.setItem(i, j, item)

    def save_changes(self):
        """保存更改到Excel文件"""
        try:
            # 从表格中收集数据
            updated_data = []
            for row in range(self.table.rowCount()):
                row_data = {}
                for col in range(self.table.columnCount()):
                    header_item = self.table.horizontalHeaderItem(col)
                    header = header_item.text() if header_item else f"Column_{col}"
                    item = self.table.item(row, col)
                    value = item.text() if item else ""
                    row_data[header] = value
                updated_data.append(row_data)

            # 读取完整的Excel文件
            full_df = pd.read_excel(SPIS_BATCHED_XLSX_FILE)

            # 更新对应批次的数据
            batch_mask = (
                (full_df['batchN'].notnull()) &
                (full_df['batchN'].apply(lambda x: str(int(x)) if pd.notnull(x) and str(x).strip() != '' else '') == str(self.batch_num))
            )

            # 删除原有的批次数据
            full_df = full_df[~batch_mask]

            # 添加更新后的数据
            if updated_data:
                updated_df = pd.DataFrame(updated_data)
                # 确保数据类型正确
                for col in updated_df.columns:
                    if col.lower() in ['order', 'batchn']:
                        updated_df[col] = pd.to_numeric(updated_df[col], errors='coerce')

                full_df = pd.concat([full_df, updated_df], ignore_index=True)

            # 保存到文件
            full_df.to_excel(SPIS_BATCHED_XLSX_FILE, index=False)

            # 更新内部数据
            self.batch_data = pd.DataFrame(updated_data)
            self.original_data = self.batch_data.copy()

            self.status_label.setText("保存成功！")
            self.status_label.setStyleSheet("color: #27ae60; font-weight: bold; padding: 10px;")

            QMessageBox.information(self, "保存成功", f"第{self.batch_num}批次的数据已成功保存到文件")

        except Exception as e:
            error_msg = f"保存失败: {str(e)}"
            self.status_label.setText("保存失败")
            self.status_label.setStyleSheet("color: #e74c3c; font-weight: bold; padding: 10px;")
            QMessageBox.critical(self, "保存错误", error_msg)

    def closeEvent(self, event):
        """窗口关闭事件"""
        # 从父窗口的字典中移除自己
        if self.parent_tool and self.batch_num in self.parent_tool.batch_detail_windows:
            del self.parent_tool.batch_detail_windows[self.batch_num]
        event.accept()

class SPISLiteratureRequestTool(QMainWindow):
    """SPIS文献求助工具图形界面"""

    def __init__(self):
        super().__init__()
        self.batch_count = 0
        self.current_data = None
        self.driver = None
        self.batch_detail_windows = {}  # 存储打开的批次详情窗口
        self.init_ui()

    def init_ui(self):
        """初始化用户界面"""
        self.setWindowTitle("📚 SPIS文献求助自动化工具")
        self.setGeometry(150, 150, 800, 700)
        self.setStyleSheet(self.get_spis_stylesheet())

        # 创建中央窗口
        central_widget = QWidget()
        self.setCentralWidget(central_widget)

        # 创建主布局
        main_layout = QVBoxLayout(central_widget)
        main_layout.setSpacing(15)
        main_layout.setContentsMargins(20, 20, 20, 20)

        # 标题
        title_label = QLabel("📚 SPIS文献求助自动化工具")
        title_label.setObjectName("spis-title")
        main_layout.addWidget(title_label)

        # 文件处理面板
        self.create_file_panel(main_layout)

        # 批次管理面板
        self.create_batch_panel(main_layout)

        # 浏览器控制面板
        self.create_browser_panel(main_layout)

        # 进度监控面板
        self.create_spis_progress_panel(main_layout)

    def create_file_panel(self, parent_layout):
        """创建文件处理面板"""
        group = QGroupBox("📁 文件处理")
        group.setObjectName("spis-group")
        layout = QVBoxLayout(group)

        # 文件选择
        file_layout = QHBoxLayout()
        self.csv_file_btn = QPushButton("📂 选择CSV文件")
        self.csv_file_btn.clicked.connect(self.select_csv_file)
        # 提升按钮响应灵敏度
        self.csv_file_btn.setAutoDefault(False)
        self.csv_file_btn.setDefault(False)
        file_layout.addWidget(self.csv_file_btn)

        self.csv_file_label = QLabel("未选择文件")
        self.csv_file_label.setStyleSheet("color: #666; font-style: italic;")
        file_layout.addWidget(self.csv_file_label)
        file_layout.addStretch()

        layout.addLayout(file_layout)

        # 处理按钮
        process_layout = QHBoxLayout()
        self.process_file_btn = QPushButton("🔄 处理文件并生成批次")
        self.process_file_btn.clicked.connect(self.process_csv_file)
        self.process_file_btn.setEnabled(False)
        # 提升按钮响应灵敏度
        self.process_file_btn.setAutoDefault(False)
        self.process_file_btn.setDefault(False)
        process_layout.addWidget(self.process_file_btn)
        process_layout.addStretch()

        layout.addLayout(process_layout)

        # 文件状态显示
        self.file_status_label = QLabel("请选择CSV文件开始处理")
        self.file_status_label.setStyleSheet("color: #666; font-style: italic;")
        layout.addWidget(self.file_status_label)

        parent_layout.addWidget(group)

    def create_batch_panel(self, parent_layout):
        """创建批次管理面板"""
        group = QGroupBox("📊 批次管理")
        group.setObjectName("spis-group")
        layout = QVBoxLayout(group)

        # 批次选择
        batch_layout = QHBoxLayout()
        batch_layout.addWidget(QLabel("选择批次:"))

        self.batch_combo = QComboBox()
        self.batch_combo.setEnabled(False)
        self.batch_combo.currentTextChanged.connect(self.on_batch_changed)
        batch_layout.addWidget(self.batch_combo)

        self.batch_info_btn = QPushButton("📋 查看批次详情")
        self.batch_info_btn.clicked.connect(self.show_batch_info)
        self.batch_info_btn.setEnabled(False)
        batch_layout.addWidget(self.batch_info_btn)

        batch_layout.addStretch()
        layout.addLayout(batch_layout)

        # 批次信息显示
        self.batch_status_label = QLabel("请先处理文件生成批次")
        self.batch_status_label.setStyleSheet("color: #666; font-style: italic;")
        layout.addWidget(self.batch_status_label)

        parent_layout.addWidget(group)

    def create_browser_panel(self, parent_layout):
        """创建浏览器控制面板"""
        group = QGroupBox("🌐 浏览器控制")
        group.setObjectName("spis-group")
        layout = QVBoxLayout(group)

        # 浏览器控制按钮
        browser_layout = QHBoxLayout()

        self.open_browser_btn = QPushButton("🚀 打开浏览器")
        self.open_browser_btn.clicked.connect(self.open_browser)
        self.open_browser_btn.setEnabled(False)
        # 提升按钮响应灵敏度
        self.open_browser_btn.setAutoDefault(False)
        self.open_browser_btn.setDefault(False)
        browser_layout.addWidget(self.open_browser_btn)

        self.start_process_btn = QPushButton("▶️ 开始处理批次")
        self.start_process_btn.clicked.connect(self.start_batch_processing)
        self.start_process_btn.setEnabled(False)
        # 提升按钮响应灵敏度
        self.start_process_btn.setAutoDefault(False)
        self.start_process_btn.setDefault(False)
        browser_layout.addWidget(self.start_process_btn)

        self.stop_process_btn = QPushButton("⏹️ 停止处理")
        self.stop_process_btn.clicked.connect(self.stop_processing)
        self.stop_process_btn.setEnabled(False)
        # 提升按钮响应灵敏度
        self.stop_process_btn.setAutoDefault(False)
        self.stop_process_btn.setDefault(False)
        browser_layout.addWidget(self.stop_process_btn)

        browser_layout.addStretch()
        layout.addLayout(browser_layout)

        # 说明文字
        info_label = QLabel("💡 使用说明：\n1. 先处理CSV文件生成批次\n2. 选择要处理的批次\n3. 打开浏览器并手动登录SPIS网站\n4. 开始自动处理")
        info_label.setStyleSheet("color: #7f8c8d; font-size: 12px; padding: 10px;")
        info_label.setWordWrap(True)
        layout.addWidget(info_label)

        parent_layout.addWidget(group)

    def create_spis_progress_panel(self, parent_layout):
        """创建进度监控面板"""
        group = QGroupBox("📈 处理进度")
        group.setObjectName("spis-group")
        layout = QVBoxLayout(group)

        # 进度条
        self.spis_progress = QProgressBar()
        self.spis_progress.setVisible(False)
        layout.addWidget(self.spis_progress)

        # 状态标签
        self.spis_status_label = QLabel("就绪")
        self.spis_status_label.setAlignment(Qt.AlignmentFlag.AlignCenter)
        layout.addWidget(self.spis_status_label)

        # 处理日志
        log_label = QLabel("📝 处理日志:")
        log_label.setStyleSheet("font-weight: bold; margin-top: 10px;")
        layout.addWidget(log_label)

        self.spis_log_text = QTextEdit()
        self.spis_log_text.setMaximumHeight(200)
        self.spis_log_text.setPlainText("等待开始处理...")
        self.spis_log_text.setReadOnly(True)
        self.spis_log_text.setStyleSheet("""
            QTextEdit {
                background-color: #f8f9fa;
                border: 1px solid #dee2e6;
                border-radius: 5px;
                padding: 8px;
                font-family: 'Consolas', 'Monaco', monospace;
                font-size: 10px;
                color: #495057;
            }
        """)
        layout.addWidget(self.spis_log_text)

        parent_layout.addWidget(group)

    def get_spis_stylesheet(self):
        """获取SPIS工具样式表"""
        return """
            QMainWindow {
                background-color: #f0f8ff;
            }
            QLabel#spis-title {
                font-size: 22px;
                font-weight: bold;
                color: #2c3e50;
                padding: 15px;
                background: qlineargradient(x1:0, y1:0, x2:1, y2:0,
                    stop:0 #e74c3c, stop:1 #c0392b);
                color: white;
                border-radius: 8px;
                margin-bottom: 10px;
            }
            QGroupBox#spis-group {
                font-size: 14px;
                font-weight: bold;
                border: 2px solid #e74c3c;
                border-radius: 8px;
                margin-top: 10px;
                padding-top: 10px;
                background-color: white;
            }
            QGroupBox#spis-group::title {
                subcontrol-origin: margin;
                left: 15px;
                padding: 0 8px 0 8px;
                color: #e74c3c;
            }
            QPushButton {
                background-color: #e74c3c;
                border: none;
                color: white;
                padding: 8px 16px;
                font-size: 12px;
                border-radius: 4px;
                font-weight: bold;
            }
            QPushButton:hover {
                background-color: #c0392b;
                border: 1px solid #a93226;
            }
            QPushButton:pressed {
                background-color: #a93226;
                border: 1px solid #922b21;
            }
            QPushButton:disabled {
                background-color: #bdc3c7;
                color: #7f8c8d;
            }
            QComboBox {
                padding: 6px;
                border: 2px solid #e74c3c;
                border-radius: 4px;
                background-color: white;
                font-size: 12px;
                min-width: 150px;
            }
            QComboBox:focus {
                border-color: #c0392b;
            }
        """

    def select_csv_file(self):
        """选择CSV文件"""
        file_path, _ = QFileDialog.getOpenFileName(
            self, "选择CSV文件", "",
            "CSV Files (*.csv)"
        )

        if file_path:
            self.csv_file_path = file_path
            self.csv_file_label.setText(f"已选择: {os.path.basename(file_path)}")
            self.csv_file_label.setStyleSheet("color: #27ae60; font-weight: bold;")
            self.process_file_btn.setEnabled(True)
            self.file_status_label.setText("文件已选择，可以开始处理")
            self.log_message(f"已选择CSV文件: {file_path}")

    def process_csv_file(self):
        """处理CSV文件"""
        try:
            self.log_message("开始处理CSV文件...")

            # 先将CSV转换为Excel，只保留DOI和Title两列
            xlsx_file = spis_csv_to_xlsx(self.csv_file_path, SPIS_XLSX_FILE)
            self.log_message(f"CSV转换完成: {xlsx_file}")

            # 添加序号和批次号
            df, batch_count = spis_add_order_and_batchN(SPIS_XLSX_FILE, SPIS_BATCHED_XLSX_FILE)
            self.batch_count = batch_count

            # 更新批次选择器
            self.batch_combo.clear()
            for i in range(1, batch_count + 1):
                self.batch_combo.addItem(f"第{i}批次")

            self.batch_combo.setEnabled(True)
            self.batch_info_btn.setEnabled(True)
            self.open_browser_btn.setEnabled(True)

            self.file_status_label.setText(f"文件处理完成！共生成 {batch_count} 个批次")
            self.file_status_label.setStyleSheet("color: #27ae60; font-weight: bold;")

            self.batch_status_label.setText(f"共 {batch_count} 个批次，请选择要处理的批次")
            self.batch_status_label.setStyleSheet("color: #27ae60; font-weight: bold;")

            self.log_message(f"文件处理完成！共生成 {batch_count} 个批次")

        except Exception as e:
            error_msg = f"处理文件时发生错误: {str(e)}"
            self.log_message(error_msg)
            QMessageBox.critical(self, "处理错误", error_msg)

    def show_batch_info(self):
        """显示批次详情 - 非模态可编辑窗口"""
        if not self.batch_combo.currentText():
            return

        batch_num = self.batch_combo.currentIndex() + 1

        # 如果该批次的详情窗口已经打开，则激活它
        if batch_num in self.batch_detail_windows:
            existing_window = self.batch_detail_windows[batch_num]
            existing_window.raise_()
            existing_window.activateWindow()
            return

        try:
            # 读取批次数据
            df = pd.read_excel(SPIS_BATCHED_XLSX_FILE)
            if 'Title' not in df.columns:
                df['Title'] = ''

            batch_df = df[
                (df['batchN'].notnull()) &
                (df['DOI'].notnull()) & (df['DOI'].astype(str).str.strip() != '') &
                (df['batchN'].apply(lambda x: str(int(x)) if pd.notnull(x) and str(x).strip() != '' else '') == str(batch_num))
            ]

            if len(batch_df) == 0:
                QMessageBox.warning(self, "批次为空", f"第{batch_num}批次没有可显示的数据")
                return

            # 创建非模态的可编辑详情窗口
            detail_window = EditableBatchDetailWindow(batch_num, batch_df, self)
            detail_window.show()

            # 保存窗口引用
            self.batch_detail_windows[batch_num] = detail_window

            self.log_message(f"已打开第{batch_num}批次详情窗口（可编辑）")

        except Exception as e:
            error_msg = f"读取批次信息失败: {str(e)}"
            self.log_message(error_msg)
            QMessageBox.critical(self, "错误", error_msg)

    def on_batch_changed(self):
        """批次选择改变时的处理"""
        if self.batch_combo.currentText():
            # 如果有浏览器正在运行，先关闭它
            if self.driver:
                try:
                    self.log_message("检测到批次切换，正在关闭当前浏览器...")
                    self.driver.quit()
                    self.driver = None
                    self.log_message("浏览器已关闭")
                except Exception as e:
                    self.log_message(f"关闭浏览器时发生错误: {str(e)}")

                # 重置按钮状态
                self.open_browser_btn.setEnabled(True)
                self.start_process_btn.setEnabled(False)
                self.stop_process_btn.setEnabled(False)

                # 提示用户需要重新打开浏览器
                self.log_message(f"已切换到{self.batch_combo.currentText()}，请重新打开浏览器并登录")

                # 显示提示对话框
                QMessageBox.information(
                    self, "批次已切换",
                    f"已切换到{self.batch_combo.currentText()}。\n\n"
                    "由于一个登录账号一次只能下载50篇文献，\n"
                    "程序已自动关闭当前浏览器。\n\n"
                    "请点击'🚀 打开浏览器'按钮重新登录。"
                )

    def open_browser(self):
        """打开浏览器"""
        try:
            self.log_message("正在启动Chrome浏览器...")
            self.driver = webdriver.Chrome(service=Service(SPIS_CHROME_DRIVER_PATH))
            self.driver.get('https://spis.hnlat.com/scholar/list?val=10.1016%2Fj.ijhm.2025.104227&oaFirst=0')

            self.start_process_btn.setEnabled(True)
            self.open_browser_btn.setEnabled(False)

            self.log_message("浏览器已启动，请手动登录SPIS网站")
            QMessageBox.information(
                self, "浏览器已启动",
                "Chrome浏览器已启动并打开SPIS网站。\n\n请手动登录您的账户，然后点击'开始处理批次'按钮。"
            )

        except Exception as e:
            error_msg = f"启动浏览器失败: {str(e)}"
            self.log_message(error_msg)
            QMessageBox.critical(self, "浏览器错误", error_msg)

    def start_batch_processing(self):
        """开始处理批次"""
        if not self.driver:
            QMessageBox.warning(self, "错误", "请先打开浏览器")
            return

        if not self.batch_combo.currentText():
            QMessageBox.warning(self, "错误", "请选择要处理的批次")
            return

        batch_num = self.batch_combo.currentIndex() + 1

        # 确认开始处理
        reply = QMessageBox.question(
            self, "确认处理",
            f"确定要开始处理第{batch_num}批次吗？\n\n请确保已经在浏览器中登录了SPIS网站。",
            QMessageBox.StandardButton.Yes | QMessageBox.StandardButton.No
        )

        if reply == QMessageBox.StandardButton.Yes:
            self.process_selected_batch(batch_num)

    def process_selected_batch(self, batch_num):
        """处理选定的批次"""
        try:
            # 读取批次数据
            df = pd.read_excel(SPIS_BATCHED_XLSX_FILE)
            if 'Title' not in df.columns:
                df['Title'] = ''

            batch_df = df[
                (df['batchN'].notnull()) &
                (df['DOI'].notnull()) & (df['DOI'].astype(str).str.strip() != '') &
                (df['batchN'].apply(lambda x: str(int(x)) if pd.notnull(x) and str(x).strip() != '' else '') == str(batch_num))
            ]

            total = len(batch_df)
            if total == 0:
                QMessageBox.warning(self, "错误", f"第{batch_num}批次没有可处理的文献")
                return

            # 设置进度条
            self.spis_progress.setVisible(True)
            self.spis_progress.setMaximum(total)
            self.spis_progress.setValue(0)

            # 更新按钮状态
            self.start_process_btn.setEnabled(False)
            self.stop_process_btn.setEnabled(True)

            self.log_message(f"开始处理第{batch_num}批次，共{total}篇文献")

            success, fail = 0, []
            start_time = time.time()

            for idx, (_, row) in enumerate(batch_df.iterrows()):
                doi = str(row['DOI']).strip()
                title = str(row['Title']).strip()

                try:
                    self.spis_status_label.setText(f"正在处理第{idx+1}/{total}篇: {title[:50]}...")
                    self.log_message(f"处理第{idx+1}篇: {title}")

                    # 查找搜索框并输入DOI
                    search_box = WebDriverWait(self.driver, SPIS_WAIT_TIME).until(
                        EC.presence_of_element_located((By.CSS_SELECTOR, "input[type='text']"))
                    )
                    search_box.clear()
                    search_box.send_keys(doi)

                    self.log_message(f"已输入DOI: {doi}")
                    self.log_message("请手动点击'搜文章'按钮，并在结果页手动点击第1篇论文下的'文献求助'按钮...")

                    # 等待用户手动操作后出现的复选框
                    checkbox = WebDriverWait(self.driver, 600).until(
                        EC.element_to_be_clickable((By.XPATH, "//input[@type='checkbox']"))
                    )
                    checkbox.click()
                    self.log_message("已自动勾选服务条款，请手动点击'确定'按钮")

                    # 等待复选框消失，表示用户已点击确定
                    WebDriverWait(self.driver, 600).until_not(
                        EC.presence_of_element_located((By.XPATH, "//input[@type='checkbox']"))
                    )

                    success += 1
                    self.spis_progress.setValue(success)
                    self.log_message(f"第{idx+1}篇处理完成")

                    # 短暂延时
                    time.sleep(random.uniform(1, 2))

                except Exception as e:
                    fail.append((doi, title, str(e)))
                    self.log_message(f"处理失败: {doi} - {title} - {str(e)}")
                    continue

            # 处理完成
            elapsed = int(time.time() - start_time)
            self.spis_status_label.setText(f"第{batch_num}批次处理完成！成功: {success}篇")

            completion_msg = f"第{batch_num}批次处理完成！\n\n"
            completion_msg += f"成功处理: {success} 篇\n"
            if fail:
                completion_msg += f"失败: {len(fail)} 篇\n"
                completion_msg += "失败详情请查看处理日志"
            completion_msg += f"\n总耗时: {elapsed // 60}分{elapsed % 60}秒"

            self.log_message(completion_msg)
            QMessageBox.information(self, "处理完成", completion_msg)

            # 重置按钮状态
            self.start_process_btn.setEnabled(True)
            self.stop_process_btn.setEnabled(False)
            self.spis_progress.setVisible(False)

        except Exception as e:
            error_msg = f"处理批次时发生错误: {str(e)}"
            self.log_message(error_msg)
            QMessageBox.critical(self, "处理错误", error_msg)

            # 重置按钮状态
            self.start_process_btn.setEnabled(True)
            self.stop_process_btn.setEnabled(False)
            self.spis_progress.setVisible(False)

    def stop_processing(self):
        """停止处理"""
        reply = QMessageBox.question(
            self, "确认停止",
            "确定要停止当前处理吗？",
            QMessageBox.StandardButton.Yes | QMessageBox.StandardButton.No
        )

        if reply == QMessageBox.StandardButton.Yes:
            self.log_message("用户手动停止处理")
            self.spis_status_label.setText("处理已停止")

            # 重置按钮状态
            self.start_process_btn.setEnabled(True)
            self.stop_process_btn.setEnabled(False)
            self.spis_progress.setVisible(False)

    def log_message(self, message):
        """添加日志消息"""
        timestamp = time.strftime("%H:%M:%S")
        log_entry = f"[{timestamp}] {message}"
        self.spis_log_text.append(log_entry)

        # 自动滚动到底部
        cursor = self.spis_log_text.textCursor()
        cursor.movePosition(cursor.MoveOperation.End)
        self.spis_log_text.setTextCursor(cursor)

        # 刷新界面
        QApplication.processEvents()

    def closeEvent(self, event):
        """窗口关闭事件"""
        if self.driver:
            try:
                self.driver.quit()
                self.log_message("浏览器已关闭")
            except:
                pass
        event.accept()

def run_spis_tool():
    """运行SPIS文献求助工具（保留原函数以兼容性）"""
    print("=" * 60)
    print("SPIS 文献求助自动化工具")
    print("=" * 60)

    # 先将CSV转换为Excel，只保留DOI和Title两列
    if not os.path.exists(SPIS_XLSX_FILE):
        spis_csv_to_xlsx(SPIS_CSV_FILE, SPIS_XLSX_FILE)
    spis_add_order_and_batchN(SPIS_XLSX_FILE, SPIS_BATCHED_XLSX_FILE)
    print("\n请指定要处理的批次编号（如1、2、3...）：")
    batch_num = input("请输入批次编号：").strip()
    spis_process_batch_xlsx(batch_num)
    print("\n如需继续处理下一批，请重新运行本脚本并输入对应批次编号。")

# ========================================
# ScreenP AI文献筛选工具部分
# ========================================

import json
import re
import subprocess
import platform
from typing import Dict, List, Optional, Tuple
from pathlib import Path
import threading
import traceback
import requests
from PyQt6.QtWidgets import *
from PyQt6.QtCore import *
from PyQt6.QtGui import *

# AI平台配置
AI_PLATFORMS = {
    "deepseek": {
        "name": "DeepSeek",
        "api_key": "***********************************",
        "base_url": "https://api.deepseek.com/v1/chat/completions",
        "models": ["deepseek-chat", "deepseek-reasoner"],
        "default_model": "deepseek-chat"
    },
    "chatanywhere": {
        "name": "ChatAnywhere", 
        "api_key": "sk-rHjR1JnETK1p6brlIU3kjbmGL6U477fk8ObNtf4NEEKKDd7a",
        "base_url": "https://api.chatanywhere.tech/v1/chat/completions",
        "models": [
            "o3", "o3-2025-04-16", "o4-mini", "o4-mini-2025-04-16", "gpt-4.1",
            "gpt-4.1-2025-04-14", "gpt-4.1-mini", "gpt-4.1-mini-2025-04-14",
            "gpt-4.1-nano", "gpt-4.1-nano-2025-04-14", "gpt-3.5-turbo",
            "gpt-3.5-turbo-1106", "gpt-3.5-turbo-0125", "gpt-3.5-turbo-16k",
            "gpt-3.5-turbo-instruct", "gpt-4.5-preview", "gpt-4.5-preview-2025-02-27",
            "o1-mini", "o1-preview", "o3-mini", "o1", "gpt-4o-search-preview",
            "gpt-4o-search-preview-2025-03-11", "gpt-4o-mini-search-preview",
            "gpt-4o-mini-search-preview-2025-03-11", "gpt-4", "gpt-4o",
            "gpt-4o-2024-05-13", "gpt-4o-2024-08-06", "gpt-4o-2024-11-20",
            "chatgpt-4o-latest", "gpt-4o-mini", "gpt-4-0613", "gpt-4-turbo-preview",
            "gpt-4-0125-preview", "gpt-4-1106-preview", "gpt-4-vision-preview",
            "gpt-4-turbo", "gpt-4-turbo-2024-04-09", "gpt-3.5-turbo-ca",
            "gpt-4-ca", "gpt-4-turbo-ca", "gpt-4o-ca", "gpt-4o-mini-ca",
            "chatgpt-4o-latest-ca", "o1-mini-ca", "o1-preview-ca", "deepseek-reasoner",
            "deepseek-r1", "deepseek-v3", "claude-3-7-sonnet-20250219",
            "claude-3-5-sonnet-20240620", "claude-3-5-sonnet-20241022",
            "claude-3-5-haiku-20241022", "gemini-1.5-flash-latest",
            "gemini-1.5-pro-latest", "gemini-exp-1206", "gemini-2.0-flash-exp",
            "gemini-2.0-pro-exp-02-05", "gemini-2.0-flash", "gemini-2.5-pro-exp-03-25",
            "gemini-2.5-pro-preview-05-06", "gemini-2.5-flash-preview-04-17",
            "grok-3", "grok-3-reasoner", "grok-3-deepsearch"
        ],
        "default_model": "gemini-2.5-flash-preview-04-17"
    }
}

# 默认提示词
DEFAULT_PROMPT = """### **【正式版】AI论文筛选任务提示词**

#### **1. 角色与任务 (Role & Task)**
**# 角色定义：一位顶尖的跨学科研究学者**
你将扮演一名在学术界享有盛誉的资深学者，你的专业知识背景具有鲜明的跨学科特征，具体层次如下：
* **宏观学科领域 (Broad Academic Fields):**
    * 你的知识根基建立在【计算机科学（人工智能）、管理学、商学、法学 (Law)、科技伦理学 (Ethics of Technology)和公共管理 (Public Administration) 】这几大领域的交叉地带。这为你提供了分析问题的法律原则、伦理框架和治理视角。
* **核心研究领域 (Specific Research Area):**
    * 在上述宏观领域的基础上，你将研究焦点收窄，专攻于 **人工智能伦理与治理 (AI Ethics and Governance)**。这是你学术身份的核心，你致力于理解和解决新兴技术对社会规范、个人权利和公共秩序带来的挑战。
* **微观研究方向 (Micro-scopic Research Focus):**
    * 在你的核心研究领域内，你已经成为一个更细分、更前沿的微观方向上的权威专家。你目前的研究兴趣和工作全部围绕着 **自动化决策系统中的责任归因与法律适用性 (Responsibility Attribution and Legal Applicability in Automated Decision-Making Systems)**。你对这个微观方向的最新动态、关键争议（如"责任真空"问题）、理论前沿（如分布式责任理论）和重要文献（如欧盟《人工智能法案》的相关条款）了如指掌。

**任务指令：**
请你完全沉浸在这个专家角色中，运用你（作为该角色）积累的全部知识、洞察力和判断标准，来审视和评估我提供给你的每一篇关于AI的论文。你的判断将尤其关注一篇文献是否能够精准切入"AI决策"与"责任归因"的连接点。
你将扮演一位专业的科研助理，拥有信息科学、伦理学和法学的交叉学科背景。你的核心任务是协助我完成一项关于"人工智能决策与责任归因"的文献综述项目。我将会一篇一篇地提交论文的标题和摘要，你需要根据我提供的资料，严格按照既定的内容标准进行筛选和评估。

#### **2. 核心筛选标准 (Core Screening Criteria)**

你需要判断每篇论文是否**直接且深入地**探讨了以下两大主题的**交叉领域**：

* **主题A：AI决策或AI辅助决策**
    * 这不仅包括明确提及"AI决策"（AI decision-making）或"AI辅助决策"（AI-assisted decision-making）的文献。
    * **也应涵盖**：算法决策（algorithmic decision-making）、自动化决策系统（automated decision systems）、决策支持系统（decision support systems）、专家系统（expert systems）、推荐系统（recommender systems）、自主系统/武器（autonomous systems/weapons）、以及在特定领域（如医疗、金融、司法、军事）中由AI/算法驱动的判断与选择过程。

* **主题B：责任或责任归因**
    * 这不仅包括明确提及"责任"（responsibility）或"责任归因"（responsibility attribution）。
    * **也应涵盖**：问责/可责性（accountability）、法律责任（liability）、道德责任（moral responsibility）、过失/罪责（culpability/blameworthiness）、"责任差距"（responsibility gap）问题、以及与责任判定紧密相关的概念，如：可解释性（explainability/XAI）、透明度（transparency）、可审计性（auditability）、公平性（fairness）和偏见（bias）等伦理及法律议题。

**关键判断点**：一篇论文必须**同时**探讨主题A和主题B，并且将两者的**关联**作为其核心议题或重要研究内容之一。如果一篇论文只讨论AI决策的技术实现（纯主题A），或只在没有AI背景下讨论责任理论（纯主题B），那么它就不符合标准。

#### **3. 任务指令 (Task Instructions)**

对于我提交的每一篇论文，请你执行以下操作：

1.  **逐步思考（Think Step-by-Step）**：首先，通读标题和摘要，识别出与**主题A**和**主题B**相关的所有概念。然后，分析论文的核心论点，判断这两个主题的**结合紧密程度**。
2.  **生成评估结果**：基于你的分析，严格按照下述"输出格式"和"评估准则"生成评估报告。

#### **4. 评估准则 (Evaluation Criteria)**

请参考以下准则来确定"符合度"的百分比：

* **80% - 100% (高度符合)**：论文的**中心主题**就是AI决策与责任的交叉研究。标题和摘要都明确反映了这一点。
* **50% - 79% (中度符合)**：论文的**一个主要部分或重要的次要论点**是关于AI决策与责任的。摘要中清晰地讨论了这两个主题的关联，但可能不是唯一的焦点。
* **20% - 49% (低度符合)**：论文的核心是关于AI技术或某个应用领域，但在文中**顺带提及或简要讨论**了相关的责任、伦理问题。这种关联不是其研究的重点。
* **0% - 19% (基本不符)**：论文**几乎没有**将AI决策与责任问题联系起来，可能只是孤立地包含其中某个主题的关键词，或者完全不相关。

#### **5. 输出格式 (Output Format)**

请严格按照此格式反馈你的评估结果（与此无关的内容一律不要输出，只需要你输出下面的内容）：

**符合度评估结果：** [请在此处填写一个0%到100%的百分比数值]%

**判断理由：**
1.  **主题A（AI决策）相关性分析**：[请在此处详细说明论文如何涉及AI决策。]
2.  **主题B（责任）相关性分析**：[请在此处详细说明论文如何涉及责任问题。]
3.  **综合评估与打分逻辑**：[请在此处综合以上两点，详细解释你为何给予这个具体的符合度分数，并阐述两个主题在文中的结合程度。]


---

**提示词使用说明：**
您可以将以上内容作为对AI的初始设定。设定完成后，您就可以直接发送第一篇论文的【标题】和【摘要】了。AI将根据此处的指令进行处理。
尽管有多篇论文的标题和摘要，但我会一篇一篇地提供给你，请你按要求一篇一篇地对论文的符合度进行评估并给出判断的理由。
以下为需要你判断的某一篇论文的标题和摘要信息：

"""

class ProgressWorker(QObject):
    """进度工作线程"""
    progress_updated = pyqtSignal(int, int, int, int, str, int)  # current, total, batch_current, batch_total, status, elapsed_seconds
    result_ready = pyqtSignal(dict)
    error_occurred = pyqtSignal(str)
    finished = pyqtSignal()
    batch_completed = pyqtSignal(int, dict, int)  # batch_number, batch_results, elapsed_seconds
    process_info_updated = pyqtSignal(str)  # 处理过程信息

    def __init__(self):
        super().__init__()
        self.data = None
        self.platform_config = None
        self.prompt = None
        self.batch_count = 1
        self.max_tokens = 4096
        self.temperature = 0.7
        self.should_stop = False
        self.is_test_mode = False
        self.auto_save_enabled = True
        self.output_path_base = None
        self.start_row_offset = 0  # 起始行偏移量，用于显示正确的全局行号
        self.start_time = None  # 开始处理时间

    def setup(self, data, platform_config, prompt, batch_count, max_tokens, temperature):
        self.data = data
        self.platform_config = platform_config
        self.prompt = prompt
        self.batch_count = batch_count
        self.max_tokens = max_tokens
        self.temperature = temperature
        self.should_stop = False

    def set_auto_save(self, enabled, output_path_base=None):
        """设置自动保存参数"""
        self.auto_save_enabled = enabled
        self.output_path_base = output_path_base

    def stop(self):
        self.should_stop = True

    def run(self):
        """执行AI筛选任务"""
        try:
            # 记录开始时间
            self.start_time = time.time()

            total_count = len(self.data)
            batch_size = max(1, total_count // self.batch_count)
            results = []

            # 在开始处理时创建初始文件（测试模式和正式模式都创建）
            if self.auto_save_enabled and self.output_path_base:
                self.create_initial_file()

            for batch_idx in range(self.batch_count):
                if self.should_stop:
                    break

                start_idx = batch_idx * batch_size
                if batch_idx == self.batch_count - 1:
                    end_idx = total_count
                else:
                    end_idx = start_idx + batch_size

                batch_data = self.data[start_idx:end_idx]
                batch_results = []

                for idx, row in enumerate(batch_data):
                    if self.should_stop:
                        break

                    global_idx = start_idx + idx
                    status = "🧪 测试处理中..." if self.is_test_mode else f"正在处理第{batch_idx + 1}批次..."

                    # 计算已用时间
                    elapsed_seconds = int(time.time() - self.start_time) if self.start_time else 0

                    self.progress_updated.emit(
                        global_idx + 1, total_count,
                        idx + 1, len(batch_data),
                        status, elapsed_seconds
                    )

                    # 发送记录编号信息，考虑起始行偏移
                    actual_row_number = self.start_row_offset + global_idx + 1
                    record_header = f"📊 记录编号：第{actual_row_number}条(原第{global_idx + 1}条)/第{batch_idx + 1}批（此批{len(batch_data)}条）/共{self.batch_count}批/共{total_count}条\n"
                    self.process_info_updated.emit(record_header)

                    # 调用AI API
                    result = self.call_ai_api(row)
                    batch_results.append(result)
                    results.append(result)

                    # 实时更新文件（每处理一条记录就更新，测试模式和正式模式都更新）
                    if self.auto_save_enabled and self.output_path_base:
                        self.update_progress_file(results)

                    # 短暂延时，避免API调用过快
                    time.sleep(0.5)

                # 批次完成后的处理
                if not self.should_stop and not self.is_test_mode:
                    elapsed_seconds = int(time.time() - self.start_time) if self.start_time else 0
                    self.batch_completed.emit(batch_idx + 1, {"results": batch_results}, elapsed_seconds)

            if not self.should_stop:
                self.result_ready.emit({"results": results, "data": self.data})

        except Exception as e:
            self.error_occurred.emit(f"处理过程中发生错误：{str(e)}")
        finally:
            self.finished.emit()

    def create_initial_file(self):
        """创建初始的结果文件"""
        try:
            # 创建初始的空DataFrame
            df = pd.DataFrame(columns=['序号', 'title', 'Abstract Note', '符合度&判断理由'])

            # 保存初始文件
            if self.output_path_base.lower().endswith('.csv'):
                df.to_csv(self.output_path_base, index=False, encoding='utf-8-sig')
            else:
                df.to_excel(self.output_path_base, index=False)

            print(f"✓ 已创建初始结果文件: {self.output_path_base}")
        except Exception as e:
            print(f"创建初始文件失败: {e}")

    def update_progress_file(self, all_results):
        """实时更新进度文件"""
        try:
            # 准备导出数据
            export_data = []
            for i, result in enumerate(all_results):
                # 直接使用AI的完整评估结果
                ai_evaluation = result.get('ai_evaluation', '无评估结果')

                # 计算实际行号（考虑起始行偏移）
                actual_row_number = self.start_row_offset + i + 1

                export_data.append({
                    '序号': actual_row_number,
                    'title': result['title'],
                    'Abstract Note': result['abstract'],
                    '符合度&判断理由': ai_evaluation
                })

            # 创建DataFrame
            df = pd.DataFrame(export_data)

            # 保存文件
            if self.output_path_base.lower().endswith('.csv'):
                df.to_csv(self.output_path_base, index=False, encoding='utf-8-sig')
            else:
                df.to_excel(self.output_path_base, index=False)

        except Exception as e:
            print(f"更新进度文件失败: {e}")

    def call_ai_api(self, row_data):
        """调用AI API进行评估"""
        title = row_data.get('title', '')
        abstract = row_data.get('Abstract Note', '')

        # 构建完整的提示
        full_prompt = self.prompt + f"\ntitle: {title}; abstract: {abstract}"

        # 发送详细的处理开始信息
        truncated_title = title[:80] + '...' if len(title) > 80 else title
        truncated_abstract = abstract[:150] + '...' if len(abstract) > 150 else abstract

        start_info = f"{'='*60}\n"
        start_info += f"📤 开始处理新记录\n"
        start_info += f"📋 论文标题：{truncated_title}\n"
        start_info += f"📄 论文摘要：{truncated_abstract}\n"
        start_info += f"🤖 使用模型：{self.platform_config.get('model', 'Unknown')}\n"
        start_info += f"🔧 最大令牌：{self.max_tokens}\n"
        start_info += f"🌡️ 温度参数：{self.temperature}\n"
        start_info += f"🔗 API端点：{self.platform_config.get('base_url', 'Unknown')}\n"
        start_info += f"⏰ 开始时间：{time.strftime('%H:%M:%S')}\n"
        start_info += f"📝 提示词长度：{len(full_prompt)} 字符\n"
        start_info += f"{'='*60}\n\n"

        self.process_info_updated.emit(start_info)

        # 重试机制参数
        max_retries = 3
        retry_delays = [2, 5, 10]  # 每次重试的延时（秒）

        for attempt in range(max_retries):
            try:
                # 发送API调用状态
                if attempt == 0:
                    api_call_info = f"🚀 发送API请求...\n"
                else:
                    api_call_info = f"🔄 第 {attempt + 1} 次重试...\n"
                api_call_info += f"   - 正在连接服务器\n"
                api_call_info += f"   - 请求超时设置：60秒\n"
                api_call_info += f"   - 等待AI响应...\n\n"

                self.process_info_updated.emit(api_call_info)

                headers = {
                    "Content-Type": "application/json",
                    "Authorization": f"Bearer {self.platform_config['api_key']}"
                }

                data = {
                    "model": self.platform_config['model'],
                    "messages": [
                        {"role": "user", "content": full_prompt}
                    ],
                    "max_tokens": self.max_tokens,
                    "temperature": self.temperature
                }

                # 记录请求开始时间
                request_start_time = time.time()

                response = requests.post(
                    self.platform_config['base_url'],
                    headers=headers,
                    json=data,
                    timeout=60
                )

                # 计算响应时间
                response_time = time.time() - request_start_time

                if response.status_code == 200:
                    result = response.json()
                    content = result['choices'][0]['message']['content']

                    # 发送成功响应信息
                    success_info = f"✅ API调用成功！\n"
                    if attempt > 0:
                        success_info += f"🎯 第 {attempt + 1} 次尝试成功\n"
                    success_info += f"⏱️ 响应时间：{response_time:.2f} 秒\n"
                    success_info += f"📊 HTTP状态：{response.status_code}\n"
                    success_info += f"📏 响应长度：{len(content)} 字符\n"
                    success_info += f"💰 估算Token使用：约 {len(full_prompt)//4 + len(content)//4} tokens\n\n"

                    self.process_info_updated.emit(success_info)

                    # 显示AI原始回复
                    raw_response_info = f"🤖 AI原始回复内容：\n"
                    raw_response_info += f"{'='*40}\n"
                    raw_response_info += f"{content}\n"
                    raw_response_info += f"{'='*40}\n\n"

                    self.process_info_updated.emit(raw_response_info)

                    # 解析结果 - 直接使用AI的完整回复
                    _, ai_full_response = self.parse_ai_response(content)

                    # 发送解析结果信息
                    parsing_info = f"🔍 结果处理：\n"
                    parsing_info += f"   - AI回复处理：✅ 成功\n"
                    parsing_info += f"   - 回复内容长度：{len(ai_full_response)} 字符\n"
                    parsing_info += f"   - 内容状态：完整保存\n\n"

                    self.process_info_updated.emit(parsing_info)

                    # 显示最终处理结果
                    final_result_info = f"📋 最终处理结果：\n"
                    final_result_info += f"   📝 AI完整评估：{ai_full_response[:300]}{'...' if len(ai_full_response) > 300 else ''}\n"
                    final_result_info += f"   ✅ 数据状态：已保存到结果集\n"
                    final_result_info += f"   ⏰ 完成时间：{time.strftime('%H:%M:%S')}\n"
                    final_result_info += f"{'='*60}\n\n"

                    self.process_info_updated.emit(final_result_info)

                    return {
                        'title': title,
                        'abstract': abstract,
                        'ai_evaluation': ai_full_response,
                        'raw_response': content
                    }
                else:
                    # HTTP错误，记录并可能重试
                    error_msg = f"HTTP {response.status_code}: {response.text[:200]}"
                    if attempt < max_retries - 1:
                        retry_info = f"⚠️ 请求失败，{retry_delays[attempt]}秒后重试...\n"
                        retry_info += f"   - 错误：{error_msg}\n"
                        retry_info += f"   - 剩余重试次数：{max_retries - attempt - 1}\n\n"
                        self.process_info_updated.emit(retry_info)
                        time.sleep(retry_delays[attempt])
                        continue
                    else:
                        # 最后一次尝试也失败了
                        error_detail = f"❌ API调用最终失败！\n"
                        error_detail += f"📊 HTTP状态码：{response.status_code}\n"
                        error_detail += f"🔍 错误详情：{error_msg}\n"
                        error_detail += f"🔄 已重试 {max_retries} 次\n"
                        error_detail += f"🔧 建议处理：检查API密钥、配额或网络连接\n"
                        error_detail += f"{'='*60}\n\n"

                        self.process_info_updated.emit(error_detail)

                        return {
                            'title': title,
                            'abstract': abstract,
                            'ai_evaluation': f'API调用失败 (重试{max_retries}次): HTTP {response.status_code} - {response.text[:100]}',
                            'raw_response': ''
                        }

            except Exception as e:
                # 网络连接异常等，进行重试
                error_type = type(e).__name__
                error_msg = str(e)

                if attempt < max_retries - 1:
                    # 还有重试机会
                    retry_info = f"⚠️ 连接异常，{retry_delays[attempt]}秒后重试...\n"
                    retry_info += f"   - 异常类型：{error_type}\n"
                    retry_info += f"   - 异常信息：{error_msg[:200]}{'...' if len(error_msg) > 200 else ''}\n"
                    retry_info += f"   - 剩余重试次数：{max_retries - attempt - 1}\n\n"
                    self.process_info_updated.emit(retry_info)
                    time.sleep(retry_delays[attempt])
                    continue
                else:
                    # 最后一次尝试也失败了
                    exception_info = f"❌ 连接最终失败！\n"
                    exception_info += f"🚨 异常类型：{error_type}\n"
                    exception_info += f"📝 异常信息：{error_msg}\n"
                    exception_info += f"🔄 已重试 {max_retries} 次\n"
                    exception_info += f"📍 可能原因：\n"
                    exception_info += f"   - 网络连接不稳定\n"
                    exception_info += f"   - API服务器临时不可用\n"
                    exception_info += f"   - 防火墙或代理问题\n"
                    exception_info += f"🔧 建议处理：检查网络连接和API配置\n"
                    exception_info += f"{'='*60}\n\n"

                    self.process_info_updated.emit(exception_info)

                    return {
                        'title': title,
                        'abstract': abstract,
                        'ai_evaluation': f'连接异常 (重试{max_retries}次): {error_type} - {str(e)[:100]}',
                        'raw_response': ''
                    }

        # 理论上不会到达这里，但作为保险
        return {
            'title': title,
            'abstract': abstract,
            'ai_evaluation': '未知错误',
            'raw_response': ''
        }

    def parse_ai_response(self, content: str) -> Tuple[str, str]:
        """解析AI响应，直接返回完整的AI回复内容"""
        try:
            # 直接返回AI的完整回复内容，不需要单独提取符合度
            # AI的回复中已经包含了符合度评估结果和完整的判断理由
            return "AI完整评估", content.strip()

        except Exception as e:
            print(f"处理AI响应时发生错误: {e}")
            return "处理错误", f"处理异常: {str(e)}"

class AILiteratureScreeningTool(QMainWindow):
    """AI文献筛选工具主窗口"""

    def __init__(self):
        super().__init__()
        self.data = None
        self.worker = None
        self.thread = None
        self.results = None
        self.result_file_path = None  # 存储导入的结果文件路径
        self.file_path = None  # 添加file_path属性
        self.init_ui()

    def init_ui(self):
        """初始化用户界面"""
        self.setWindowTitle("🔬 AI文献筛选自动化工具")
        self.setGeometry(100, 100, 1200, 1000)  # 增加高度从800到1000
        self.setStyleSheet(self.get_stylesheet())

        # 创建中央窗口
        central_widget = QWidget()
        self.setCentralWidget(central_widget)

        # 创建主布局
        main_layout = QVBoxLayout(central_widget)
        main_layout.setSpacing(20)
        main_layout.setContentsMargins(20, 20, 20, 20)

        # 创建滚动区域
        scroll_area = QScrollArea()
        scroll_area.setWidgetResizable(True)
        scroll_area.setHorizontalScrollBarPolicy(Qt.ScrollBarPolicy.ScrollBarAsNeeded)
        scroll_area.setVerticalScrollBarPolicy(Qt.ScrollBarPolicy.ScrollBarAsNeeded)
        scroll_area.setMinimumHeight(900)  # 设置最小高度确保内容可见

        scroll_widget = QWidget()
        scroll_widget.setMinimumHeight(900)  # 设置内容widget的最小高度
        scroll_layout = QVBoxLayout(scroll_widget)

        # 标题
        title_label = QLabel("🔬 AI文献筛选自动化工具")
        title_label.setObjectName("title")
        scroll_layout.addWidget(title_label)

        # AI模型配置面板
        self.create_ai_config_panel(scroll_layout)

        # 提示词配置面板
        self.create_prompt_panel(scroll_layout)

        # 数据导入和任务配置面板
        self.create_data_panel(scroll_layout)

        # 进度监控面板
        self.create_progress_panel(scroll_layout)

        scroll_area.setWidget(scroll_widget)
        main_layout.addWidget(scroll_area)

        # 确保窗口有足够的显示空间
        self.showMaximized()  # 启动时最大化窗口
        self.setMinimumSize(1200, 1000)  # 设置最小窗口尺寸

    def create_ai_config_panel(self, parent_layout):
        """创建AI模型配置面板"""
        group = QGroupBox("🤖 AI模型配置")
        group.setObjectName("config-group")
        layout = QVBoxLayout(group)

        # 平台选择
        platform_layout = QHBoxLayout()
        platform_layout.addWidget(QLabel("选择AI平台:"))

        self.platform_combo = QComboBox()
        for key, config in AI_PLATFORMS.items():
            self.platform_combo.addItem(config["name"], key)
        self.platform_combo.currentTextChanged.connect(self.on_platform_changed)
        platform_layout.addWidget(self.platform_combo)
        platform_layout.addStretch()

        layout.addLayout(platform_layout)

        # API Key配置
        api_layout = QHBoxLayout()
        api_layout.addWidget(QLabel("API Key:"))

        self.api_key_input = QLineEdit()
        self.api_key_input.setEchoMode(QLineEdit.EchoMode.Password)  # 隐藏输入内容
        self.api_key_input.setPlaceholderText("API密钥已预设，如需修改请输入新的密钥")
        api_layout.addWidget(self.api_key_input)

        # 显示/隐藏API Key按钮
        self.show_api_key_btn = QPushButton("👁")
        self.show_api_key_btn.setMaximumWidth(40)
        self.show_api_key_btn.setToolTip("显示/隐藏API Key")
        self.show_api_key_btn.clicked.connect(self.toggle_api_key_visibility)
        api_layout.addWidget(self.show_api_key_btn)

        layout.addLayout(api_layout)

        # 模型选择
        model_layout = QHBoxLayout()
        model_layout.addWidget(QLabel("选择模型:"))

        self.model_combo = QComboBox()
        self.model_combo.setMinimumWidth(400)  # 设置最小宽度
        self.model_combo.setSizeAdjustPolicy(QComboBox.SizeAdjustPolicy.AdjustToContents)  # 根据内容调整大小
        model_layout.addWidget(self.model_combo)
        model_layout.addStretch()

        layout.addLayout(model_layout)

        # 参数配置
        params_layout = QGridLayout()

        # Max Tokens
        params_layout.addWidget(QLabel("Max Tokens:"), 0, 0)
        self.max_tokens_slider = QSlider(Qt.Orientation.Horizontal)
        self.max_tokens_slider.setRange(0, 8192)
        self.max_tokens_slider.setValue(4096)
        self.max_tokens_slider.valueChanged.connect(self.update_max_tokens_input)
        params_layout.addWidget(self.max_tokens_slider, 0, 1)

        self.max_tokens_input = QSpinBox()
        self.max_tokens_input.setRange(0, 8192)
        self.max_tokens_input.setValue(4096)
        self.max_tokens_input.valueChanged.connect(self.update_max_tokens_slider)
        params_layout.addWidget(self.max_tokens_input, 0, 2)

        # Temperature
        params_layout.addWidget(QLabel("Temperature:"), 1, 0)
        self.temperature_slider = QSlider(Qt.Orientation.Horizontal)
        self.temperature_slider.setRange(0, 200)  # 0-2.00
        self.temperature_slider.setValue(70)      # 0.70
        self.temperature_slider.valueChanged.connect(self.update_temperature_input)
        params_layout.addWidget(self.temperature_slider, 1, 1)

        self.temperature_input = QDoubleSpinBox()
        self.temperature_input.setRange(0.0, 2.0)
        self.temperature_input.setSingleStep(0.01)
        self.temperature_input.setDecimals(2)  # 设置小数位数
        self.temperature_input.setValue(0.7)
        self.temperature_input.valueChanged.connect(self.update_temperature_slider)
        params_layout.addWidget(self.temperature_input, 1, 2)

        layout.addLayout(params_layout)

        parent_layout.addWidget(group)

        # 初始化默认平台
        self.on_platform_changed()

    def toggle_api_key_visibility(self):
        """切换API Key显示/隐藏"""
        if self.api_key_input.echoMode() == QLineEdit.EchoMode.Password:
            self.api_key_input.setEchoMode(QLineEdit.EchoMode.Normal)
            self.show_api_key_btn.setText("🙈")
        else:
            self.api_key_input.setEchoMode(QLineEdit.EchoMode.Password)
            self.show_api_key_btn.setText("👁")

    def on_platform_changed(self):
        """平台选择改变时的处理"""
        platform_key = self.platform_combo.currentData()
        if platform_key:
            config = AI_PLATFORMS[platform_key]
            self.model_combo.clear()
            self.model_combo.addItems(config["models"])

            # 设置默认模型
            default_model = config["default_model"]
            index = self.model_combo.findText(default_model)
            if index >= 0:
                self.model_combo.setCurrentIndex(index)

            # 设置默认API Key
            self.api_key_input.setText(config["api_key"])
            self.api_key_input.setPlaceholderText(f"默认API Key已设置 ({config['name']})")

    def update_max_tokens_input(self, value):
        """更新Max Tokens输入框"""
        self.max_tokens_input.setValue(value)

    def update_max_tokens_slider(self, value):
        """更新Max Tokens滑块"""
        self.max_tokens_slider.setValue(value)

    def update_temperature_input(self, value):
        """更新Temperature输入框"""
        self.temperature_input.setValue(value / 100.0)

    def update_temperature_slider(self, value):
        """更新Temperature滑块"""
        self.temperature_slider.setValue(int(value * 100))

    def create_prompt_panel(self, parent_layout):
        """创建提示词配置面板"""
        group = QGroupBox("📝 筛选标准提示词")
        group.setObjectName("config-group")
        layout = QVBoxLayout(group)

        self.prompt_text = QTextEdit()
        self.prompt_text.setPlaceholderText("请在此处粘贴您的筛选标准提示词...")
        self.prompt_text.setMinimumHeight(300)
        self.prompt_text.setPlainText(DEFAULT_PROMPT)  # 设置默认提示词
        layout.addWidget(self.prompt_text)

        parent_layout.addWidget(group)

    def create_data_panel(self, parent_layout):
        """创建数据导入和任务配置面板"""
        group = QGroupBox("📊 数据导入与任务配置")
        group.setObjectName("config-group")
        layout = QVBoxLayout(group)

        # 文件导入
        file_layout = QHBoxLayout()
        self.import_btn = QPushButton("📁 导入文献文件")
        self.import_btn.clicked.connect(self.import_file)
        file_layout.addWidget(self.import_btn)

        self.file_label = QLabel("未选择文件")
        self.file_label.setStyleSheet("color: #666; font-style: italic;")
        file_layout.addWidget(self.file_label)
        file_layout.addStretch()

        layout.addLayout(file_layout)

        # 处理范围配置
        range_group = QGroupBox("🎯 处理范围设置")
        range_group.setStyleSheet("QGroupBox { font-size: 14px; }")
        range_layout = QGridLayout(range_group)

        # 起始行
        range_layout.addWidget(QLabel("起始行:"), 0, 0)
        self.start_row_spin = QSpinBox()
        self.start_row_spin.setRange(1, 999999)
        self.start_row_spin.setValue(1)
        self.start_row_spin.valueChanged.connect(self.update_range_info)
        range_layout.addWidget(self.start_row_spin, 0, 1)

        # 结束行
        range_layout.addWidget(QLabel("结束行:"), 0, 2)
        self.end_row_spin = QSpinBox()
        self.end_row_spin.setRange(1, 999999)
        self.end_row_spin.setValue(1)
        self.end_row_spin.valueChanged.connect(self.update_range_info)
        range_layout.addWidget(self.end_row_spin, 0, 3)

        # 快速设置按钮
        self.all_rows_btn = QPushButton("📋 全部行")
        self.all_rows_btn.clicked.connect(self.set_all_rows)
        self.all_rows_btn.setEnabled(False)
        self.all_rows_btn.setToolTip("设置为处理全部数据")
        range_layout.addWidget(self.all_rows_btn, 0, 4)

        # 范围信息显示
        self.range_info_label = QLabel("请先导入文件")
        self.range_info_label.setStyleSheet("color: #666; font-style: italic;")
        range_layout.addWidget(self.range_info_label, 1, 0, 1, 5)

        layout.addWidget(range_group)

        # 批次配置
        batch_layout = QHBoxLayout()
        batch_layout.addWidget(QLabel("总批次数:"))

        self.batch_count_spin = QSpinBox()
        self.batch_count_spin.setRange(1, 50)
        self.batch_count_spin.setValue(5)
        self.batch_count_spin.valueChanged.connect(self.update_batch_info)
        batch_layout.addWidget(self.batch_count_spin)

        self.batch_info_label = QLabel("")
        batch_layout.addWidget(self.batch_info_label)
        batch_layout.addStretch()

        layout.addLayout(batch_layout)

        # 实时保存配置
        save_layout = QHBoxLayout()
        self.auto_save_checkbox = QCheckBox("启用实时保存")
        self.auto_save_checkbox.setChecked(True)
        self.auto_save_checkbox.setToolTip("每完成一个批次自动保存中间结果")
        save_layout.addWidget(self.auto_save_checkbox)
        save_layout.addStretch()

        layout.addLayout(save_layout)

        # 控制按钮
        control_layout = QHBoxLayout()

        # 测试运行按钮
        self.test_btn = QPushButton("🧪 测试运行 (前5条)")
        self.test_btn.clicked.connect(self.test_screening)
        self.test_btn.setEnabled(False)
        self.test_btn.setToolTip("处理前5条记录，验证程序配置是否正确")
        control_layout.addWidget(self.test_btn)

        # 正式运行按钮
        self.run_btn = QPushButton("🚀 正式运行")
        self.run_btn.clicked.connect(self.run_screening)
        self.run_btn.setEnabled(False)
        control_layout.addWidget(self.run_btn)

        # 中止按钮
        self.stop_btn = QPushButton("⏹ 中止")
        self.stop_btn.clicked.connect(self.stop_screening)
        self.stop_btn.setEnabled(False)
        control_layout.addWidget(self.stop_btn)

        control_layout.addStretch()

        layout.addLayout(control_layout)

        parent_layout.addWidget(group)

    def create_progress_panel(self, parent_layout):
        """创建进度监控面板"""
        group = QGroupBox("📈 进度监控")
        group.setObjectName("config-group")
        layout = QVBoxLayout(group)

        # 主进度条
        self.main_progress = QProgressBar()
        self.main_progress.setVisible(False)
        layout.addWidget(self.main_progress)

        # 状态标签
        self.status_label = QLabel("就绪")
        self.status_label.setAlignment(Qt.AlignmentFlag.AlignCenter)
        layout.addWidget(self.status_label)

        # 处理过程显示框
        process_label = QLabel("🔍 处理过程详情:")
        process_label.setStyleSheet("font-weight: bold; margin-top: 10px;")
        layout.addWidget(process_label)

        self.process_text = QTextEdit()
        self.process_text.setMaximumHeight(250)  # 减少高度从300到250
        self.process_text.setPlainText("等待开始处理...")
        self.process_text.setReadOnly(True)
        self.process_text.setStyleSheet("""
            QTextEdit {
                background-color: #f8f9fa;
                border: 1px solid #dee2e6;
                border-radius: 5px;
                padding: 8px;
                font-family: 'Consolas', 'Monaco', monospace;
                font-size: 10px;
                color: #495057;
            }
        """)
        layout.addWidget(self.process_text)

        # 说明文字
        info_label = QLabel("💡 提示：建议先进行测试运行，验证配置正确后再正式运行")
        info_label.setStyleSheet("color: #7f8c8d; font-style: italic; font-size: 12px;")
        info_label.setWordWrap(True)
        layout.addWidget(info_label)

        # 导出按钮
        self.export_btn = QPushButton("💾 导出结果")
        self.export_btn.clicked.connect(self.export_results)
        self.export_btn.setEnabled(False)
        layout.addWidget(self.export_btn)

        parent_layout.addWidget(group)

    def get_stylesheet(self):
        """获取样式表"""
        return """
            QMainWindow {
                background-color: #f5f5f5;
            }
            QLabel#title {
                font-size: 24px;
                font-weight: bold;
                color: #2c3e50;
                padding: 20px;
                background: qlineargradient(x1:0, y1:0, x2:1, y2:0,
                    stop:0 #3498db, stop:1 #2980b9);
                color: white;
                border-radius: 10px;
                margin-bottom: 10px;
            }
            QGroupBox {
                font-size: 16px;
                font-weight: bold;
                border: 2px solid #bdc3c7;
                border-radius: 10px;
                margin-top: 10px;
                padding-top: 10px;
                background-color: white;
            }
            QGroupBox#config-group {
                background: qlineargradient(x1:0, y1:0, x2:0, y2:1,
                    stop:0 #ffffff, stop:1 #f8f9fa);
            }
            QGroupBox::title {
                subcontrol-origin: margin;
                left: 20px;
                padding: 0 10px 0 10px;
                color: #2c3e50;
            }
            QPushButton {
                background-color: #3498db;
                border: none;
                color: white;
                padding: 10px 20px;
                font-size: 14px;
                border-radius: 5px;
                font-weight: bold;
            }
            QPushButton:hover {
                background-color: #2980b9;
                border: 1px solid #21618c;
            }
            QPushButton:pressed {
                background-color: #21618c;
                border: 1px solid #1b4f72;
            }
            QPushButton:disabled {
                background-color: #bdc3c7;
                color: #7f8c8d;
            }
            QComboBox, QSpinBox, QDoubleSpinBox {
                padding: 8px;
                border: 2px solid #bdc3c7;
                border-radius: 5px;
                background-color: white;
                font-size: 14px;
                min-width: 200px;
            }
            QComboBox {
                min-width: 400px;
            }
            QComboBox QAbstractItemView {
                border: 2px solid #3498db;
                selection-background-color: #3498db;
                selection-color: white;
                background-color: white;
                min-width: 400px;
            }
            QComboBox:focus, QSpinBox:focus, QDoubleSpinBox:focus {
                border-color: #3498db;
            }
            QLineEdit {
                padding: 8px;
                border: 2px solid #bdc3c7;
                border-radius: 5px;
                background-color: white;
                font-size: 14px;
            }
            QLineEdit:focus {
                border-color: #3498db;
            }
            QTextEdit {
                border: 2px solid #bdc3c7;
                border-radius: 5px;
                padding: 10px;
                font-family: 'Consolas', 'Monaco', monospace;
                font-size: 12px;
                background-color: #f8f9fa;
            }
            QTextEdit:focus {
                border-color: #3498db;
            }
            QProgressBar {
                border: 2px solid #bdc3c7;
                border-radius: 5px;
                text-align: center;
                font-weight: bold;
                font-size: 14px;
            }
            QProgressBar::chunk {
                background-color: qlineargradient(x1:0, y1:0, x2:0, y2:1,
                    stop:0 #3498db, stop:1 #2980b9);
                border-radius: 3px;
            }
            QSlider::groove:horizontal {
                border: 1px solid #bdc3c7;
                height: 8px;
                background: #ecf0f1;
                margin: 2px 0;
                border-radius: 4px;
            }
            QSlider::handle:horizontal {
                background: #3498db;
                border: 1px solid #2980b9;
                width: 18px;
                margin: -2px 0;
                border-radius: 9px;
            }
            QSlider::handle:horizontal:hover {
                background: #2980b9;
            }
        """

    def import_file(self):
        """导入文献文件"""
        file_path, _ = QFileDialog.getOpenFileName(
            self, "选择文献文件", "",
            "Excel/CSV Files (*.xlsx *.xls *.csv)"
        )

        if file_path:
            try:
                # 根据文件扩展名选择读取方法
                if file_path.endswith('.csv'):
                    # 自动检测CSV文件编码
                    encodings = ['utf-8', 'gbk', 'gb2312', 'utf-8-sig', 'latin1', 'cp1252']
                    df = None
                    used_encoding = None

                    for encoding in encodings:
                        try:
                            df = pd.read_csv(file_path, encoding=encoding)
                            used_encoding = encoding
                            print(f"✓ 成功使用 {encoding} 编码读取文件")
                            break
                        except UnicodeDecodeError:
                            print(f"✗ {encoding} 编码读取失败，尝试下一个...")
                            continue
                        except Exception as e:
                            print(f"✗ 使用 {encoding} 编码时发生其他错误: {e}")
                            continue

                    if df is None:
                        QMessageBox.critical(
                            self, "编码错误",
                            "无法识别文件编码格式。\n\n"
                            "请尝试：\n"
                            "1. 用Excel打开CSV文件，另存为UTF-8格式\n"
                            "2. 或将文件转换为Excel格式(.xlsx)"
                        )
                        return
                else:
                    df = pd.read_excel(file_path)
                    used_encoding = "Excel格式"

                # 显示原始数据信息
                original_rows = len(df)
                print(f"原始文件行数: {original_rows} (包括标题行)")
                print(f"数据行数: {original_rows - 1}")

                # 打印调试信息，显示实际的列名
                print("CSV文件中的列名:")
                for i, col in enumerate(df.columns):
                    print(f"  {i+1}. '{col}'")

                # 创建列名映射（忽略大小写和前后空格）
                column_mapping = {}
                available_columns = [col.strip().lower() for col in df.columns]
                original_columns = [col.strip() for col in df.columns]

                # 查找title列
                title_col = None
                for i, col in enumerate(available_columns):
                    if col in ['title', '标题', 'titles', 'paper title', 'article title']:
                        title_col = original_columns[i]
                        break

                # 查找Abstract Note列
                abstract_col = None
                for i, col in enumerate(available_columns):
                    if col in ['abstract note', 'abstract', '摘要', 'abstracts', 'note', 'abstract notes', 'summary']:
                        abstract_col = original_columns[i]
                        break

                # 检查是否找到了必要的列
                if title_col is None:
                    available_cols = ', '.join([f"'{col}'" for col in df.columns])
                    QMessageBox.warning(
                        self, "未找到标题列",
                        f"未能找到标题列。\n\n"
                        f"支持的标题列名: 'title', '标题', 'titles', 'paper title', 'article title'\n\n"
                        f"文件中的列名: {available_cols}\n\n"
                        f"请确保文件包含标题列，或重命名相应列。"
                    )
                    return

                if abstract_col is None:
                    available_cols = ', '.join([f"'{col}'" for col in df.columns])
                    QMessageBox.warning(
                        self, "未找到摘要列",
                        f"未能找到摘要列。\n\n"
                        f"支持的摘要列名: 'abstract note', 'abstract', '摘要', 'abstracts', 'note', 'abstract notes', 'summary'\n\n"
                        f"文件中的列名: {available_cols}\n\n"
                        f"请确保文件包含摘要列，或重命名相应列。"
                    )
                    return

                # 重命名列名以统一格式
                df = df.rename(columns={title_col: 'title', abstract_col: 'Abstract Note'})

                # 详细的数据清洗过程
                print("\n=== 数据清洗过程 ===")

                # 统计清洗前的数据
                before_cleaning = len(df)
                print(f"清洗前数据行数: {before_cleaning}")

                # 1. 检查title列的空值情况
                title_na_count = df['title'].isna().sum()
                title_empty_count = (df['title'].astype(str).str.strip() == '').sum()
                print(f"标题列空值数量: {title_na_count}")
                print(f"标题列空字符串数量: {title_empty_count}")

                # 2. 检查Abstract Note列的空值情况
                abstract_na_count = df['Abstract Note'].isna().sum()
                abstract_empty_count = (df['Abstract Note'].astype(str).str.strip() == '').sum()
                print(f"摘要列空值数量: {abstract_na_count}")
                print(f"摘要列空字符串数量: {abstract_empty_count}")

                # 3. 过滤掉标题或摘要为空的行
                df_cleaned = df.dropna(subset=['title', 'Abstract Note'])
                after_dropna = len(df_cleaned)
                print(f"删除空值后数据行数: {after_dropna}")

                # 4. 移除标题和摘要为空字符串的行
                df_cleaned = df_cleaned[
                    (df_cleaned['title'].astype(str).str.strip() != '') &
                    (df_cleaned['Abstract Note'].astype(str).str.strip() != '')
                ]
                after_cleaning = len(df_cleaned)
                print(f"删除空字符串后数据行数: {after_cleaning}")

                # 计算被过滤的行数
                filtered_rows = before_cleaning - after_cleaning
                print(f"总共过滤掉的行数: {filtered_rows}")

                self.data = df_cleaned.to_dict('records')
                self.file_path = file_path

                self.file_label.setText(f"已导入 {len(self.data)} 条记录 - {os.path.basename(file_path)}")
                self.file_label.setStyleSheet("color: #27ae60; font-weight: bold;")

                # 初始化行数范围控件
                self.start_row_spin.setMaximum(len(self.data))
                self.end_row_spin.setMaximum(len(self.data))
                self.end_row_spin.setValue(len(self.data))
                self.all_rows_btn.setEnabled(True)

                # 更新范围和批次信息
                self.update_range_info()

                # 显示详细的导入报告
                report_text = (
                    f"文件导入成功！\n\n"
                    f"📁 文件编码: {used_encoding}\n"
                    f"📊 原始数据: {original_rows} 行 (含标题行)\n"
                    f"📋 有效数据: {original_rows - 1} 行\n"
                    f"✅ 最终导入: {len(self.data)} 条记录\n"
                    f"🗑️ 过滤掉: {filtered_rows} 条记录\n\n"
                    f"列名映射:\n"
                    f"• 标题列: '{title_col}' → 'title'\n"
                    f"• 摘要列: '{abstract_col}' → 'Abstract Note'\n\n"
                    f"过滤原因:\n"
                    f"• 标题为空: {title_na_count + title_empty_count} 条\n"
                    f"• 摘要为空: {abstract_na_count + abstract_empty_count} 条"
                )

                QMessageBox.information(self, "导入报告", report_text)

                self.run_btn.setEnabled(True)
                self.test_btn.setEnabled(True)
                self.update_batch_info()

            except Exception as e:
                error_msg = f"无法读取文件：{str(e)}"
                if "codec" in str(e).lower() or "encoding" in str(e).lower():
                    error_msg += "\n\n编码问题解决方案:\n"
                    error_msg += "1. 用Excel打开CSV文件，选择'另存为'\n"
                    error_msg += "2. 选择'UTF-8 CSV'格式保存\n"
                    error_msg += "3. 或将文件转换为Excel格式(.xlsx)"

                QMessageBox.critical(self, "导入失败", error_msg)

    def update_batch_info(self):
        """更新批次信息"""
        if self.data:
            # 获取处理范围
            start_row = self.start_row_spin.value()
            end_row = self.end_row_spin.value()

            # 确保范围有效
            start_row = max(1, start_row)
            end_row = min(len(self.data), end_row)

            if start_row > end_row:
                self.batch_info_label.setText("起始行不能大于结束行")
                self.batch_info_label.setStyleSheet("color: #e74c3c; font-weight: bold;")
                return

            # 计算实际要处理的记录数
            range_count = end_row - start_row + 1
            batch_count = self.batch_count_spin.value()
            batch_size = max(1, range_count // batch_count)

            # 计算最后一个批次的大小
            last_batch_size = range_count - (batch_count - 1) * batch_size

            if last_batch_size == batch_size:
                # 所有批次大小相等
                self.batch_info_label.setText(
                    f"将处理第{start_row}-{end_row}行（共{range_count}条记录），分为{batch_count}批次，每批{batch_size}条"
                )
            else:
                # 最后一个批次大小不同
                self.batch_info_label.setText(
                    f"将处理第{start_row}-{end_row}行（共{range_count}条记录），分为{batch_count}批次，前{batch_count-1}批每批{batch_size}条，最后一批{last_batch_size}条"
                )

            # 重置样式
            self.batch_info_label.setStyleSheet("color: #27ae60; font-weight: bold;")

    def update_range_info(self):
        """更新范围信息"""
        if self.data:
            total_count = len(self.data)
            start_row = self.start_row_spin.value()
            end_row = self.end_row_spin.value()

            # 确保范围在有效范围内
            if start_row > total_count:
                start_row = total_count
                self.start_row_spin.setValue(start_row)

            if end_row > total_count:
                end_row = total_count
                self.end_row_spin.setValue(end_row)

            if start_row > end_row:
                self.range_info_label.setText("起始行不能大于结束行")
                self.range_info_label.setStyleSheet("color: #e74c3c; font-weight: bold;")
            else:
                range_count = end_row - start_row + 1
                self.range_info_label.setText(f"将处理第{start_row}-{end_row}行，共{range_count}条记录")
                self.range_info_label.setStyleSheet("color: #27ae60; font-weight: bold;")

            # 更新批次信息
            self.update_batch_info()

    def set_all_rows(self):
        """设置为处理全部行"""
        if self.data:
            self.start_row_spin.setValue(1)
            self.end_row_spin.setValue(len(self.data))

    def test_screening(self):
        """测试运行（处理前5条记录）"""
        if not self.validate_inputs():
            return

        # 获取前5条记录进行测试
        test_data = self.data[:5]

        # 设置测试模式
        self.start_screening_process(test_data, is_test=True)

    def run_screening(self):
        """正式运行筛选"""
        if not self.validate_inputs():
            return

        # 获取处理范围
        start_row = self.start_row_spin.value()
        end_row = self.end_row_spin.value()

        # 获取指定范围的数据（注意：数组索引从0开始，但用户输入从1开始）
        range_data = self.data[start_row-1:end_row]

        # 开始正式处理
        self.start_screening_process(range_data, is_test=False, start_row_offset=start_row-1)

    def validate_inputs(self):
        """验证输入参数"""
        if not self.data:
            QMessageBox.warning(self, "数据错误", "请先导入文献文件")
            return False

        prompt = self.prompt_text.toPlainText().strip()
        if not prompt:
            QMessageBox.warning(self, "提示词错误", "请输入筛选标准提示词")
            return False

        api_key = self.api_key_input.text().strip()
        if not api_key:
            QMessageBox.warning(self, "API Key错误", "请输入API Key")
            return False

        return True

    def start_screening_process(self, data, is_test=False, start_row_offset=0):
        """开始筛选处理"""
        # 获取配置
        platform_key = self.platform_combo.currentData()
        platform_config = AI_PLATFORMS[platform_key].copy()

        # 更新API Key
        api_key = self.api_key_input.text().strip()
        if api_key:
            platform_config['api_key'] = api_key

        # 更新模型
        platform_config['model'] = self.model_combo.currentText()

        prompt = self.prompt_text.toPlainText().strip()
        batch_count = 1 if is_test else self.batch_count_spin.value()
        max_tokens = self.max_tokens_input.value()
        temperature = self.temperature_input.value()

        # 创建工作线程
        self.thread = QThread()
        self.worker = ProgressWorker()
        self.worker.moveToThread(self.thread)

        # 设置工作参数
        self.worker.setup(data, platform_config, prompt, batch_count, max_tokens, temperature)
        self.worker.is_test_mode = is_test
        self.worker.start_row_offset = start_row_offset

        # 设置自动保存
        if self.auto_save_checkbox.isChecked():
            timestamp = time.strftime("%Y%m%d_%H%M%S")
            if is_test:
                filename = f"测试结果_{timestamp}.xlsx"
            else:
                filename = f"筛选结果_{timestamp}.xlsx"
            self.worker.set_auto_save(True, filename)

        # 连接信号
        self.worker.progress_updated.connect(self.update_progress)
        self.worker.process_info_updated.connect(self.update_process_info)
        self.worker.result_ready.connect(self.on_result_ready)
        self.worker.error_occurred.connect(self.on_error)
        self.worker.finished.connect(self.on_finished)
        self.worker.batch_completed.connect(self.on_batch_completed)

        # 启动线程
        self.thread.started.connect(self.worker.run)
        self.thread.start()

        # 更新UI状态
        self.main_progress.setVisible(True)
        self.main_progress.setMaximum(len(data))
        self.run_btn.setEnabled(False)
        self.test_btn.setEnabled(False)
        self.stop_btn.setEnabled(True)

        mode_text = "🧪 测试模式" if is_test else "🚀 正式运行"
        self.status_label.setText(f"{mode_text} - 准备中...")
        self.process_text.clear()
        self.process_text.append(f"{mode_text}开始，共{len(data)}条记录\n")

    def stop_screening(self):
        """停止筛选"""
        if self.worker:
            self.worker.stop()
            self.status_label.setText("正在停止...")

    def update_progress(self, current, total, batch_current, batch_total, status, elapsed_seconds):
        """更新进度"""
        self.main_progress.setValue(current)

        # 计算时间信息
        elapsed_str = f"{elapsed_seconds//60}分{elapsed_seconds%60}秒"

        # 估算剩余时间
        if current > 0:
            avg_time_per_item = elapsed_seconds / current
            remaining_items = total - current
            remaining_seconds = int(avg_time_per_item * remaining_items)
            remaining_str = f"{remaining_seconds//60}分{remaining_seconds%60}秒"
        else:
            remaining_str = "计算中..."

        status_text = f"{status} ({current}/{total}) - 已用时: {elapsed_str} - 预计剩余: {remaining_str}"
        self.status_label.setText(status_text)

    def update_process_info(self, info):
        """更新处理过程信息"""
        self.process_text.append(info)
        # 自动滚动到底部
        cursor = self.process_text.textCursor()
        cursor.movePosition(cursor.MoveOperation.End)
        self.process_text.setTextCursor(cursor)

    def on_result_ready(self, result):
        """处理完成"""
        self.results = result
        self.export_btn.setEnabled(True)

        results_count = len(result['results'])
        self.status_label.setText(f"✅ 处理完成！共处理 {results_count} 条记录")
        self.process_text.append(f"\n🎉 所有记录处理完成！共 {results_count} 条\n")

    def on_error(self, error_msg):
        """处理错误"""
        self.status_label.setText("❌ 处理出错")
        self.process_text.append(f"\n❌ 错误: {error_msg}\n")
        QMessageBox.critical(self, "处理错误", error_msg)

    def on_finished(self):
        """线程结束清理"""
        if self.thread:
            self.thread.quit()
            self.thread.wait()
            self.thread = None

        self.worker = None
        self.main_progress.setVisible(False)
        self.run_btn.setEnabled(True)
        self.test_btn.setEnabled(True)
        self.stop_btn.setEnabled(False)

    def on_batch_completed(self, batch_number, batch_results, elapsed_seconds):
        """批次完成处理"""
        elapsed_str = f"{elapsed_seconds//60}分{elapsed_seconds%60}秒"
        self.process_text.append(f"\n✅ 第{batch_number}批次完成！用时: {elapsed_str}\n")

    def export_results(self):
        """导出结果"""
        if not self.results:
            QMessageBox.warning(self, "导出错误", "没有可导出的结果")
            return

        file_path, _ = QFileDialog.getSaveFileName(
            self, "保存结果文件",
            f"筛选结果_{time.strftime('%Y%m%d_%H%M%S')}.xlsx",
            "Excel Files (*.xlsx);;CSV Files (*.csv)"
        )

        if file_path:
            try:
                # 准备导出数据
                export_data = []
                for i, result in enumerate(self.results['results']):
                    # 直接使用AI的完整评估结果
                    ai_evaluation = result.get('ai_evaluation', '无评估结果')

                    export_data.append({
                        '序号': i + 1,
                        'title': result['title'],
                        'Abstract Note': result['abstract'],
                        '符合度&判断理由': ai_evaluation
                    })

                # 创建DataFrame
                df = pd.DataFrame(export_data)

                # 保存文件
                if file_path.lower().endswith('.csv'):
                    df.to_csv(file_path, index=False, encoding='utf-8-sig')
                else:
                    df.to_excel(file_path, index=False)

                QMessageBox.information(
                    self, "导出成功",
                    f"结果已成功导出到：\n{file_path}\n\n共导出 {len(export_data)} 条记录"
                )

            except Exception as e:
                QMessageBox.critical(self, "导出失败", f"导出过程中发生错误：{str(e)}")

def run_screenp_tool():
    """运行ScreenP AI文献筛选工具（保留原函数以兼容性）"""
    print("=" * 60)
    print("ScreenP AI文献筛选工具")
    print("=" * 60)

    app = QApplication(sys.argv)
    window = AILiteratureScreeningTool()
    window.show()
    sys.exit(app.exec())

# ========================================
# 主程序入口 - 同时启动两个窗口
# ========================================

def main():
    """主程序入口 - 同时启动两个工具窗口"""
    print("=" * 80)
    print("SPIS+ScreenP 整合工具")
    print("=" * 80)
    print("正在启动两个工具窗口...")
    print("- SPIS文献求助工具窗口")
    print("- ScreenP AI文献筛选工具窗口")
    print("=" * 80)

    # 创建QApplication实例
    app = QApplication(sys.argv)

    # 创建并显示SPIS工具窗口
    spis_window = SPISLiteratureRequestTool()
    spis_window.show()

    # 创建并显示ScreenP工具窗口
    screenp_window = AILiteratureScreeningTool()
    screenp_window.show()

    # 调整窗口位置，避免重叠
    spis_window.move(100, 100)  # SPIS窗口位置
    screenp_window.move(950, 100)  # ScreenP窗口位置（右侧）

    print("✅ 两个工具窗口已启动！")
    print("📚 SPIS文献求助工具 - 红色主题窗口")
    print("🔬 ScreenP AI文献筛选工具 - 蓝色主题窗口")
    print("\n💡 提示：两个工具完全独立，可以同时使用")

    # 运行应用程序
    sys.exit(app.exec())

if __name__ == '__main__':
    main()
