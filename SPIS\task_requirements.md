# SPIS文献DOI自动化处理系统 - 技术需求规范

## 1. 项目背景与目标

### 1.1 项目背景
随着学术研究的深入发展，研究人员需要大量查阅和获取文献资料。传统的人工逐个搜索和申请文献的方式效率低下，特别是在处理大批量DOI文献时。SPIS（Scholar Paper Information System）文献DOI自动化处理系统应运而生，旨在通过自动化技术显著提升文献获取效率。

### 1.2 项目目标
- **主要目标**: 实现批量DOI文献的自动化处理和求助申请
- **效率提升**: 将人工处理时间从平均每篇2-3分钟降低到30秒以内
- **准确性保证**: 确保100%的数据完整性和处理准确性
- **用户体验**: 提供简单易用的操作界面和清晰的进度反馈

### 1.3 应用场景
- 学术研究机构批量获取文献资料
- 高校图书馆文献采购和管理
- 研究生和博士生论文撰写期间的文献收集
- 科研项目文献调研和资料整理

## 2. 功能需求详细规范

### 2.1 数据预处理模块

#### 2.1.1 CSV文件读取与解析
**功能描述**: 智能读取和解析原始CSV文件
**具体要求**:
- 支持多种分隔符自动检测（逗号、分号、制表符）
- 自动识别文件编码格式（UTF-8、GBK、GB2312）
- 容错处理：处理包含特殊字符或格式异常的数据

**输入格式**:
```csv
Title,DOI,Author,Journal,Year,...
"Research on Tourism Development",10.1016/j.ijhm.2025.104227,"Zhang Wei","International Journal","2024",...
"Sustainable Tourism Analysis","","Li Ming","Tourism Review","2023",...
```

**输出要求**:
- 只保留Title和DOI两列
- 自动去除多余的空格和特殊字符
- 统一数据格式

#### 2.1.2 数据清洗与补全
**功能描述**: 对提取的数据进行清洗和完整性检查
**具体要求**:

1. **列名标准化**:
   - 不区分大小写匹配`title`、`Title`、`TITLE`
   - 不区分大小写匹配`doi`、`DOI`、`Doi`
   - 支持中文列名：`标题`、`题目`对应Title；`DOI号`、`文献号`对应DOI

2. **数据补全逻辑**:
   ```python
   # 伪代码示例
   if DOI_field.is_empty() or DOI_field.is_null():
       DOI_field = Title_field
   ```

3. **数据验证**:
   - 检查DOI格式的基本合理性（可选功能）
   - 统计空值、重复值数量
   - 生成数据质量报告

#### 2.1.3 Excel格式转换
**功能描述**: 将处理后的数据保存为Excel格式
**技术规范**:
- 使用`openpyxl`库确保兼容性
- 设置适当的列宽度以便阅读
- 添加数据验证规则（可选）

### 2.2 批次管理模块

#### 2.2.1 自动分批逻辑
**功能描述**: 根据配置的批次大小自动分组处理
**具体实现**:

```python
# 分批逻辑示例
BATCH_SIZE = 50  # 每批处理数量（可配置）

def assign_batch_numbers(dataframe):
    batch_number = 1
    for index in range(0, len(dataframe)):
        if index > 0 and index % BATCH_SIZE == 0:
            batch_number += 1
        dataframe.loc[index, 'batchN'] = batch_number
    return dataframe
```

**批次编号规则**:
- 第1批：1-50条记录，批次号 = 1
- 第2批：51-100条记录，批次号 = 2
- 以此类推...

#### 2.2.2 数据结构设计
**最终Excel文件结构**:
| 列名 | 数据类型 | 说明 | 示例值 |
|------|----------|------|--------|
| order | 整数 | 顺序编号，从1开始 | 1, 2, 3... |
| DOI | 文本 | DOI号或Title | "10.1016/j.ijhm.2025.104227" |
| batchN | 整数 | 批次编号 | 1, 1, 1..., 2, 2, 2... |
| Title | 文本 | 原始标题 | "Research on Tourism Development" |

#### 2.2.3 批次统计报告
**输出格式**:
```
共计有DOI信息的文献：88篇
共分为2批
第1批：50篇
第2批：38篇

数据质量报告：
- 原始DOI有效记录：45篇
- DOI为空需补全：43篇
- 重复记录：0篇
```

### 2.3 浏览器自动化模块

#### 2.3.1 WebDriver配置
**技术规范**:
- 使用Selenium 4.0+版本
- 支持Chrome浏览器（主流版本）
- ChromeDriver版本自动检测和提示

**浏览器配置**:
```python
chrome_options = Options()
chrome_options.add_argument('--disable-blink-features=AutomationControlled')
chrome_options.add_experimental_option("excludeSwitches", ["enable-automation"])
chrome_options.add_experimental_option('useAutomationExtension', False)
```

#### 2.3.2 页面元素定位策略
**搜索框定位**:
- 主定位器：`input[type='text']`
- 备用定位器：`//input[@placeholder='请输入DOI或关键词']`
- 容错机制：等待元素出现，最长等待时间可配置

**按钮定位策略**:
- 搜索按钮：通过文本内容定位 `//button[contains(text(), '搜文章')]`
- 文献求助按钮：`//a[contains(text(), '文献求助')]`
- 服务条款复选框：`//input[@type='checkbox']`

#### 2.3.3 人机交互流程设计
**自动化与人工配合的最佳实践**:

1. **自动化部分**（无需人工干预）:
   - 清空搜索框
   - 输入DOI内容
   - 等待页面加载
   - 自动勾选服务条款复选框

2. **人工操作部分**（需要用户手动完成）:
   - 点击"搜文章"按钮
   - 点击"文献求助"按钮
   - 点击"确定"按钮

3. **交互提示设计**:
   ```
   [自动] 已填入DOI: 10.1016/j.ijhm.2025.104227
   [提示] 请手动点击"搜文章"按钮...
   [等待] 等待页面加载中...
   [自动] 已找到"文献求助"按钮
   [提示] 请手动点击"文献求助"按钮...
   [自动] 已勾选服务条款
   [提示] 请手动点击"确定"按钮完成...
   ```

### 2.4 进度监控与反馈模块

#### 2.4.1 实时进度显示
**进度信息格式**:
```
[批次1/3] 处理进度: 15/50 (30%)
当前处理: Tourist Rural Destination Restorative Capacity: Scale Development
预计剩余时间: 约12分钟
平均处理速度: 2.3篇/分钟
```

#### 2.4.2 调试信息输出
**详细调试模式**:
```
[DEBUG] 开始处理批次1，共50条记录
[DEBUG] 正在定位搜索框元素...
[DEBUG] 搜索框定位成功: <input type="text" class="search-input">
[DEBUG] 已清空搜索框
[DEBUG] 已输入DOI: 10.1016/j.ijhm.2025.104227
[INFO]  等待用户手动点击"搜文章"按钮...
[DEBUG] 检测到页面跳转，开始查找"文献求助"按钮...
```

#### 2.4.3 错误处理与记录
**异常分类与处理**:

1. **网络相关错误**:
   - 页面加载超时
   - 网络连接中断
   - 服务器响应异常

2. **元素定位错误**:
   - 页面结构变化导致的定位失败
   - 动态加载元素未及时出现
   - JavaScript执行错误

3. **数据相关错误**:
   - DOI格式异常
   - 搜索结果为空
   - 文献求助按钮不可用

**错误记录格式**:
```json
{
  "timestamp": "2024-12-xx 10:30:15",
  "batch_number": 1,
  "record_index": 15,
  "doi": "10.1016/j.ijhm.2025.104227",
  "title": "Research Article Title",
  "error_type": "ElementNotFound",
  "error_message": "未找到文献求助按钮",
  "retry_count": 2,
  "final_status": "failed"
}
```

## 3. 技术规范与架构设计

### 3.1 系统架构
```
SPIS文献处理系统
├── 数据层 (Data Layer)
│   ├── CSV读取模块
│   ├── Excel操作模块
│   └── 数据验证模块
├── 业务逻辑层 (Business Logic Layer)
│   ├── 批次管理模块
│   ├── 数据处理模块
│   └── 流程控制模块
├── 自动化层 (Automation Layer)
│   ├── WebDriver管理
│   ├── 页面操作封装
│   └── 异常处理机制
└── 交互层 (Interaction Layer)
    ├── 命令行界面
    ├── 进度显示
    └── 用户提示
```

### 3.2 开发环境要求
**Python环境**:
- Python 3.7.0 或更高版本
- 推荐使用Python 3.9+ 以获得更好的性能

**依赖包版本规范**:
```txt
pandas>=1.3.0,<2.0.0
openpyxl>=3.0.9,<4.0.0
selenium>=4.0.0,<5.0.0
```

**开发工具推荐**:
- IDE: PyCharm Professional / VSCode
- 版本控制: Git
- 包管理: pip + requirements.txt
- 虚拟环境: venv 或 conda

### 3.3 代码质量标准
**编码规范**:
- 遵循PEP 8编码风格
- 使用类型注解 (Type Hints)
- 函数和类必须有完整的文档字符串

**示例代码规范**:
```python
def csv_to_xlsx(csv_file: str, xlsx_file: str) -> str:
    """
    将CSV文件转换为Excel格式，只保留Title和DOI两列
    
    Args:
        csv_file (str): 输入CSV文件路径
        xlsx_file (str): 输出Excel文件路径
    
    Returns:
        str: 转换后的Excel文件路径
    
    Raises:
        FileNotFoundError: 当输入文件不存在时
        ValueError: 当文件格式不符合要求时
    """
    # 实现代码...
```

**测试要求**:
- 单元测试覆盖率≥80%
- 集成测试覆盖主要功能流程
- 异常情况测试完整

## 4. 输入输出规范

### 4.1 输入数据规格

#### 4.1.1 原始CSV文件要求
**文件名**: `导出的条目.csv`
**必需列**:
- `Title` 或 `标题`: 文献标题
- `DOI` 或 `DOI号`: 数字对象标识符

**可选列**:
- `Author` / `作者`: 作者信息
- `Journal` / `期刊`: 期刊名称
- `Year` / `年份`: 发表年份

**数据格式示例**:
```csv
Title,DOI,Author,Journal,Year
"Sustainable Tourism Development in Rural Areas",10.1016/j.tourman.2024.104567,"Zhang Wei, Li Ming","Tourism Management",2024
"Digital Transformation in Hospitality","","Wang Qiang","International Journal of Hospitality Management",2023
"Cultural Heritage and Tourism",,,"Journal of Cultural Heritage",2024
```

#### 4.1.2 数据质量要求
- **编码格式**: UTF-8（推荐）或GBK
- **分隔符**: 逗号、分号或制表符
- **引用符**: 支持双引号包围字段
- **最大文件大小**: 不超过50MB
- **最大记录数**: 建议不超过10,000条

### 4.2 输出数据规格

#### 4.2.1 生成文件列表
1. **`导出的条目.xlsx`**: 清洗后的数据文件
2. **`带批次_导出的条目.xlsx`**: 添加批次信息的完整文件
3. **`处理日志.txt`**: 详细的处理日志（可选）
4. **`错误报告.json`**: 错误记录的结构化数据（可选）

#### 4.2.2 Excel文件格式规范
**列定义**:
```python
COLUMN_DEFINITIONS = {
    'order': {
        'type': 'integer',
        'description': '顺序编号',
        'range': '1 to N',
        'example': 1
    },
    'DOI': {
        'type': 'string',
        'description': 'DOI号或替代标题',
        'max_length': 500,
        'example': '10.1016/j.ijhm.2025.104227'
    },
    'batchN': {
        'type': 'integer',
        'description': '批次编号',
        'range': '1 to M',
        'example': 1
    },
    'Title': {
        'type': 'string',
        'description': '原始文献标题',
        'max_length': 1000,
        'example': 'Research on Tourism Development'
    }
}
```

#### 4.2.3 终端输出格式
**标准输出格式**:
```
========================================
SPIS文献DOI自动化处理系统 v1.0.0
========================================

[信息] 正在读取文件: 导出的条目.csv
[信息] 检测到分隔符: 逗号 (,)
[信息] 检测到编码格式: UTF-8
[信息] 共读取记录: 88条

[信息] 数据清洗完成:
  - 保留有效记录: 88条
  - DOI为空已补全: 43条
  - 重复记录已去除: 0条

[信息] 批次分配完成:
  - 共分为: 2批
  - 第1批: 50条
  - 第2批: 38条

请指定要处理的批次编号（如1、2、3...）：
> 1

[信息] 开始处理第1批，共50条记录
[信息] 浏览器启动成功，请手动登录并按回车继续...

[进度] 1/50 - 当前处理: Research on Tourism Development
[自动] 已填入DOI: 10.1016/j.ijhm.2025.104227
[提示] 请手动点击"搜文章"按钮...
[自动] 已勾选服务条款
[提示] 请手动点击"确定"按钮...
[完成] 处理成功

[进度] 2/50 - 当前处理: Sustainable Tourism Analysis
...

========================================
第1批次处理完成！
========================================
成功处理: 48篇 (96%)
失败记录: 2篇 (4%)
总耗时: 15分32秒
平均速度: 3.1篇/分钟

失败详情:
1. 10.1177/00472875231213992 - Tourist Destination Analysis
   错误: 页面加载超时
2. 10.1145/3139352 - Digital Tourism Platform  
   错误: 未找到"文献求助"按钮

如需继续处理下一批，请重新运行脚本。
```

## 5. 验收标准与测试规范

### 5.1 功能验收标准

#### 5.1.1 数据处理功能
✅ **CSV读取与转换**:
- [ ] 正确识别多种分隔符格式
- [ ] 准确提取Title和DOI两列
- [ ] 成功转换为Excel格式
- [ ] DOI为空时正确用Title补全

✅ **批次管理功能**:
- [ ] 按配置大小正确分批（默认50条/批）
- [ ] 准确添加order顺序编号
- [ ] 正确分配batchN批次编号
- [ ] 生成准确的分批统计报告

#### 5.1.2 自动化操作功能
✅ **浏览器控制**:
- [ ] 成功启动Chrome浏览器
- [ ] 正确打开目标网站
- [ ] 准确定位页面元素
- [ ] 稳定执行自动化操作

✅ **交互流程**:
- [ ] 自动填写DOI到搜索框
- [ ] 等待用户手动操作的提示清晰
- [ ] 自动勾选服务条款复选框
- [ ] 正确处理页面跳转和弹窗

#### 5.1.3 异常处理功能
✅ **错误处理**:
- [ ] 网络超时时的优雅处理
- [ ] 元素定位失败时的重试机制
- [ ] 详细记录失败原因和上下文
- [ ] 支持跳过失败记录继续处理

### 5.2 性能验收标准

#### 5.2.1 处理效率
- **单条记录处理时间**: ≤30秒（包含人工操作时间）
- **批量处理效率**: 50条记录在25分钟内完成
- **内存使用**: 处理1000条记录时内存占用≤200MB
- **文件大小**: 处理后Excel文件大小合理（原始数据的1.2-1.5倍）

#### 5.2.2 稳定性要求
- **连续运行时间**: 支持连续运行2小时不崩溃
- **错误恢复**: 单次失败不影响后续处理
- **资源清理**: 程序结束时正确关闭浏览器和清理临时文件

### 5.3 用户体验验收标准

#### 5.3.1 易用性
- **安装简便**: 依赖安装过程不超过5分钟
- **操作直观**: 新用户能在10分钟内完成首次使用
- **提示清晰**: 所有人工操作步骤都有明确提示
- **错误友好**: 错误信息用户能够理解并知道如何解决

#### 5.3.2 反馈机制
- **实时进度**: 实时显示处理进度和剩余时间
- **状态清晰**: 明确区分自动化操作和人工操作
- **结果报告**: 处理完成后提供详细的统计报告

### 5.4 兼容性验收标准

#### 5.4.1 环境兼容性
✅ **操作系统**:
- [ ] Windows 10 (1809及以上版本)
- [ ] Windows 11 (所有版本)

✅ **Python版本**:
- [ ] Python 3.7.x
- [ ] Python 3.8.x  
- [ ] Python 3.9.x
- [ ] Python 3.10.x
- [ ] Python 3.11.x

✅ **浏览器兼容**:
- [ ] Chrome 100+
- [ ] Chrome 110+
- [ ] Chrome 120+ (最新版本)

#### 5.4.2 数据格式兼容性
✅ **输入格式**:
- [ ] UTF-8编码的CSV文件
- [ ] GBK编码的CSV文件
- [ ] 逗号分隔的CSV文件
- [ ] 分号分隔的CSV文件
- [ ] 制表符分隔的CSV文件

### 5.5 测试用例设计

#### 5.5.1 正常流程测试
```python
def test_normal_workflow():
    """测试正常的完整工作流程"""
    # 1. 准备测试数据
    create_test_csv_file(records=10)
    
    # 2. 执行数据预处理
    result_xlsx = csv_to_xlsx('test_input.csv', 'test_output.xlsx')
    assert os.path.exists(result_xlsx)
    
    # 3. 执行批次分配
    df, batch_count = add_order_and_batchN(result_xlsx, 'test_batched.xlsx')
    assert batch_count == 1  # 10条记录应该只有1批
    
    # 4. 验证数据完整性
    assert len(df) == 10
    assert 'order' in df.columns
    assert 'batchN' in df.columns
```

#### 5.5.2 异常情况测试
```python
def test_exception_handling():
    """测试异常情况的处理"""
    # 测试文件不存在
    with pytest.raises(FileNotFoundError):
        csv_to_xlsx('nonexistent.csv', 'output.xlsx')
    
    # 测试格式错误的CSV
    with pytest.raises(ValueError):
        csv_to_xlsx('invalid_format.csv', 'output.xlsx')
    
    # 测试缺少必需列
    with pytest.raises(ValueError):
        csv_to_xlsx('missing_columns.csv', 'output.xlsx')
```

#### 5.5.3 性能测试
```python
def test_performance():
    """测试性能指标"""
    import time
    
    # 创建大量测试数据
    create_test_csv_file(records=1000)
    
    start_time = time.time()
    csv_to_xlsx('large_test.csv', 'large_output.xlsx')
    processing_time = time.time() - start_time
    
    # 性能要求：1000条记录处理时间不超过30秒
    assert processing_time < 30
```

## 6. 部署与维护规范

### 6.1 部署清单
**文件清单**:
```
SPIS/
├── spis.py                    # 主程序文件
├── README.md                  # 用户文档
├── task_requirements.md       # 技术规范
├── requirements.txt           # 依赖配置
├── chromedriver.exe          # 浏览器驱动
├── 导出的条目.csv           # 输入数据样例
└── logs/                     # 日志目录（可选）
```

**安装脚本**:
```batch
@echo off
echo 安装SPIS文献处理系统...
echo.

echo 1. 检查Python环境...
python --version
if errorlevel 1 (
    echo 错误：未检测到Python环境，请先安装Python 3.7+
    pause
    exit
)

echo 2. 安装依赖包...
pip install -r requirements.txt

echo 3. 验证安装...
python -c "import pandas, selenium, openpyxl; print('依赖包安装成功')"

echo.
echo 安装完成！运行 'python spis.py' 开始使用
pause
```

### 6.2 维护与更新

#### 6.2.1 版本管理
- 使用语义化版本号（如v1.0.0）
- 主版本号：重大功能变更或不兼容更新
- 次版本号：新增功能或功能增强
- 修订版本号：错误修复和小幅改进

#### 6.2.2 更新策略
- **热修复**：紧急bug修复，24小时内发布
- **功能更新**：月度发布周期
- **重大更新**：季度发布周期

#### 6.2.3 向后兼容性
- 配置文件格式保持向后兼容
- 输出文件格式不破坏现有数据
- API接口保持稳定

## 7. 安全与合规要求

### 7.1 数据安全
- 本地处理，不上传敏感数据到外部服务器
- 临时文件使用后自动清理
- 用户登录信息不存储或记录

### 7.2 网站使用合规
- 遵守目标网站的使用条款和服务协议
- 实现合理的请求频率控制，避免对服务器造成压力
- 提供清晰的免责声明

### 7.3 开源许可
- 使用MIT许可证发布
- 清晰标注第三方库的许可证信息
- 提供完整的版权声明

---

**文档版本**: v1.0.0  
**创建日期**: 2024年12月  
**最后更新**: 2024年12月  
**维护者**: SPIS开发团队

---

> **注意**: 本文档为技术规范文档，用于指导开发和测试工作。使用本系统时请确保遵守相关法律法规和网站使用条款。 